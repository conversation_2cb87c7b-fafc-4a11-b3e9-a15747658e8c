"use client";

import { useNavigation } from "@/context/NavigationContext";

/**
 * Higher-order function to wrap API calls with loading state management
 * @param {Function} apiFunction - The API function to wrap
 * @param {string} endpoint - The API endpoint (used for tracking loading state)
 * @returns {Function} - The wrapped API function
 */
export const withLoading = (apiFunction, endpoint) => {
  return async (...args) => {
    // Get the navigation context from the global window object
    // This is necessary because this function is not a React component
    if (typeof window !== "undefined" && window.__navigationContext) {
      const { startApiLoading, endApiLoading } = window.__navigationContext;

      try {
        // Start loading
        startApiLoading(endpoint);

        // Call the original API function
        const result = await apiFunction(...args);

        return result;
      } catch (error) {
        // Re-throw the error to be handled by the caller
        throw error;
      } finally {
        // End loading immediately for better navigation performance
        // Only add minimal delay for very fast API calls to prevent flicker
        const minLoadingTime = 100; // Reduced from 500ms to 100ms
        setTimeout(() => {
          endApiLoading(endpoint);
        }, minLoadingTime);
      }
    } else {
      // If navigation context is not available, just call the API function
      return apiFunction(...args);
    }
  };
};

/**
 * React hook to expose the navigation context to the global window object
 * This allows non-React functions to access the navigation context
 */
export const useApiLoader = () => {
  const navigation = useNavigation();

  // Expose the navigation context to the global window object
  if (typeof window !== "undefined") {
    window.__navigationContext = {
      startApiLoading: navigation.startApiLoading,
      endApiLoading: navigation.endApiLoading,
    };
  }

  return navigation;
};
