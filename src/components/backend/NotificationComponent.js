"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";

export default function NotificationComponent() {
  const router = useRouter();
  const [isShowMore, setIsShowMore] = useState({});
  const [isOpenNotification, setIsOpenNotification] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  // Fetch notifications and unread count
  const fetchNotifications = async () => {
    try {
      setIsLoading(true);
      const notificationAPI = (await import("../notifications"))
        .notificationAPI;

      // Get recent notifications
      const notificationsResponse =
        await notificationAPI.getRecentNotifications();
      if (notificationsResponse.status && notificationsResponse.data) {
        setNotifications(notificationsResponse.data.data || []);
      }

      // Get unread count
      const countResponse = await notificationAPI.getUnreadCount();
      if (countResponse.status && countResponse.data) {
        setUnreadCount(countResponse.data.unread_count || 0);
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
      toast.error("Failed to load notifications");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle mark as read
  const handleMarkAsRead = async (notificationId) => {
    try {
      const notificationAPI = (await import("../notifications"))
        .notificationAPI;
      await notificationAPI.markAsRead(notificationId, true);

      // Update local state
      setNotifications((prev) =>
        prev.map((notification) =>
          notification.id === notificationId
            ? { ...notification, is_read: true }
            : notification
        )
      );

      // Update unread count
      setUnreadCount((prev) => Math.max(0, prev - 1));
      toast.success("Notification marked as read");
    } catch (error) {
      console.error("Error marking notification as read:", error);
      toast.error("Failed to mark notification as read");
    }
  };

  // Format relative time
  const formatRelativeTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return "Just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800)
      return `${Math.floor(diffInSeconds / 86400)}d ago`;

    return date.toLocaleDateString();
  };

  // Fetch notifications on component mount
  useEffect(() => {
    fetchNotifications();

    // Auto-refresh disabled to prevent unwanted behavior
    // If you need auto-refresh, uncomment the lines below:
    // const interval = setInterval(fetchNotifications, 30000); // 30 seconds
    // return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative">
      <div>
        <div
          className="inline-flex items-center text-white cursor-pointer relative"
          onClick={() => setIsOpenNotification(!isOpenNotification)}
        >
          <div className="relative">
            <svg
              width={20}
              height={20}
              viewBox="0 0 20 20"
              className="mr-2"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M17.499 5.55501C17.3518 5.6312 17.1805 5.64585 17.0226 5.59575C16.8646 5.54565 16.7331 5.43489 16.6568 5.28782C16.0602 4.1093 15.1572 3.11321 14.0427 2.40423C13.9732 2.36042 13.9131 2.30336 13.8657 2.23631C13.8183 2.16925 13.7846 2.09352 13.7664 2.01343C13.7483 1.93334 13.7461 1.85046 13.76 1.76953C13.7739 1.68859 13.8036 1.61119 13.8474 1.54173C13.8912 1.47227 13.9483 1.41212 14.0153 1.36472C14.0824 1.31731 14.1581 1.28358 14.2382 1.26544C14.3183 1.24731 14.4012 1.24512 14.4821 1.25902C14.563 1.27291 14.6404 1.30261 14.7099 1.34642C16.0088 2.17809 17.0635 3.33983 17.7661 4.71282C17.8423 4.85995 17.857 5.0313 17.8069 5.18922C17.7568 5.34715 17.646 5.47871 17.499 5.55501ZM2.7888 5.62532C2.9033 5.62528 3.01559 5.59377 3.11341 5.53426C3.21123 5.47474 3.29082 5.38949 3.34349 5.28782C3.94005 4.1093 4.84303 3.11321 5.95755 2.40423C6.09783 2.31576 6.19721 2.17518 6.23384 2.01343C6.27046 1.85168 6.24134 1.682 6.15286 1.54173C6.06439 1.40146 5.92381 1.30207 5.76206 1.26544C5.60031 1.22882 5.43064 1.25794 5.29036 1.34642C3.99146 2.17809 2.93675 3.33983 2.23411 4.71282C2.18477 4.80807 2.16077 4.91442 2.16443 5.02163C2.16808 5.12884 2.19927 5.23331 2.25499 5.32497C2.31072 5.41664 2.38909 5.49242 2.48258 5.54502C2.57607 5.59762 2.68153 5.62528 2.7888 5.62532ZM17.3279 13.7456C17.4386 13.9355 17.4973 14.1512 17.498 14.371C17.4988 14.5908 17.4416 14.8069 17.3322 14.9976C17.2227 15.1882 17.065 15.3466 16.8748 15.4568C16.6846 15.567 16.4688 15.6252 16.249 15.6253H13.0615C12.918 16.3318 12.5348 16.9669 11.9766 17.4231C11.4185 17.8792 10.7198 18.1285 9.99896 18.1285C9.27811 18.1285 8.57943 17.8792 8.0213 17.4231C7.46316 16.9669 7.0799 16.3318 6.93646 15.6253H3.74896C3.52929 15.6249 3.31361 15.5666 3.12365 15.4563C2.93369 15.3459 2.77616 15.1875 2.66693 14.9969C2.55769 14.8063 2.50061 14.5903 2.50143 14.3706C2.50226 14.151 2.56095 13.9354 2.67161 13.7456C3.37552 12.5308 3.74896 10.8034 3.74896 8.75032C3.74896 7.09272 4.40744 5.50301 5.57954 4.33091C6.75164 3.1588 8.34135 2.50032 9.99896 2.50032C11.6566 2.50032 13.2463 3.1588 14.4184 4.33091C15.5905 5.50301 16.249 7.09272 16.249 8.75032C16.249 10.8027 16.6224 12.53 17.3279 13.7456ZM11.7661 15.6253H8.23177C8.36126 15.9905 8.60068 16.3066 8.91713 16.5301C9.23358 16.7537 9.61151 16.8737 9.99896 16.8737C10.3864 16.8737 10.7643 16.7537 11.0808 16.5301C11.3972 16.3066 11.6367 15.9905 11.7661 15.6253ZM16.249 14.3753C15.4177 12.948 14.999 11.0558 14.999 8.75032C14.999 7.42424 14.4722 6.15247 13.5345 5.21479C12.5968 4.27711 11.325 3.75032 9.99896 3.75032C8.67287 3.75032 7.4011 4.27711 6.46342 5.21479C5.52574 6.15247 4.99896 7.42424 4.99896 8.75032C4.99896 11.0566 4.57864 12.9488 3.74896 14.3753H16.249Z"
                fill="white"
              />
            </svg>
            {/* Unread Count Badge */}
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {unreadCount > 99 ? "99+" : unreadCount}
              </span>
            )}
          </div>
          Notifications
        </div>
        {isOpenNotification && (
          <div className="w-96 h-[600px] overflow-hidden origin-top-right rounded-xl bg-[#282828] overflow-y-auto text-sm/6 text-white transition duration-100 ease-out absolute right-0 mt-2 z-50">
            <div className="h-full w-full">
              <div className="border-b border-white/50 p-3 flex justify-between items-center">
                <p className="text-sm/6 font-semibold">
                  Notifications {unreadCount > 0 && `(${unreadCount} unread)`}
                </p>
                <div className="flex items-center gap-2">
                  <button
                    onClick={fetchNotifications}
                    disabled={isLoading}
                    className="text-white/70 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Refresh notifications"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className={`w-5 h-5 transition-transform ${
                        isLoading ? "animate-spin" : ""
                      }`}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"
                      />
                    </svg>
                  </button>
                  <button
                    onClick={() => setIsOpenNotification(false)}
                    className="text-white/70 hover:text-white"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="w-5 h-5"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <div className="p-2 h-[80%]">
                {isLoading ? (
                  <div className="h-full w-full flex flex-col items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                    <p className="text-sm mt-2">Loading notifications...</p>
                  </div>
                ) : notifications.length === 0 ? (
                  <div className="h-full w-full flex flex-col items-center justify-center">
                    <svg
                      width={100}
                      height={100}
                      viewBox="0 0 20 20"
                      className="mr-2 w-[100px] h-[100px]"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M17.499 5.55501C17.3518 5.6312 17.1805 5.64585 17.0226 5.59575C16.8646 5.54565 16.7331 5.43489 16.6568 5.28782C16.0602 4.1093 15.1572 3.11321 14.0427 2.40423C13.9732 2.36042 13.9131 2.30336 13.8657 2.23631C13.8183 2.16925 13.7846 2.09352 13.7664 2.01343C13.7483 1.93334 13.7461 1.85046 13.76 1.76953C13.7739 1.68859 13.8036 1.61119 13.8474 1.54173C13.8912 1.47227 13.9483 1.41212 14.0153 1.36472C14.0824 1.31731 14.1581 1.28358 14.2382 1.26544C14.3183 1.24731 14.4012 1.24512 14.4821 1.25902C14.563 1.27291 14.6404 1.30261 14.7099 1.34642C16.0088 2.17809 17.0635 3.33983 17.7661 4.71282C17.8423 4.85995 17.857 5.0313 17.8069 5.18922C17.7568 5.34715 17.646 5.47871 17.499 5.55501ZM2.7888 5.62532C2.9033 5.62528 3.01559 5.59377 3.11341 5.53426C3.21123 5.47474 3.29082 5.38949 3.34349 5.28782C3.94005 4.1093 4.84303 3.11321 5.95755 2.40423C6.09783 2.31576 6.19721 2.17518 6.23384 2.01343C6.27046 1.85168 6.24134 1.682 6.15286 1.54173C6.06439 1.40146 5.92381 1.30207 5.76206 1.26544C5.60031 1.22882 5.43064 1.25794 5.29036 1.34642C3.99146 2.17809 2.93675 3.33983 2.23411 4.71282C2.18477 4.80807 2.16077 4.91442 2.16443 5.02163C2.16808 5.12884 2.19927 5.23331 2.25499 5.32497C2.31072 5.41664 2.38909 5.49242 2.48258 5.54502C2.57607 5.59762 2.68153 5.62528 2.7888 5.62532ZM17.3279 13.7456C17.4386 13.9355 17.4973 14.1512 17.498 14.371C17.4988 14.5908 17.4416 14.8069 17.3322 14.9976C17.2227 15.1882 17.065 15.3466 16.8748 15.4568C16.6846 15.567 16.4688 15.6252 16.249 15.6253H13.0615C12.918 16.3318 12.5348 16.9669 11.9766 17.4231C11.4185 17.8792 10.7198 18.1285 9.99896 18.1285C9.27811 18.1285 8.57943 17.8792 8.0213 17.4231C7.46316 16.9669 7.0799 16.3318 6.93646 15.6253H3.74896C3.52929 15.6249 3.31361 15.5666 3.12365 15.4563C2.93369 15.3459 2.77616 15.1875 2.66693 14.9969C2.55769 14.8063 2.50061 14.5903 2.50143 14.3706C2.50226 14.151 2.56095 13.9354 2.67161 13.7456C3.37552 12.5308 3.74896 10.8034 3.74896 8.75032C3.74896 7.09272 4.40744 5.50301 5.57954 4.33091C6.75164 3.1588 8.34135 2.50032 9.99896 2.50032C11.6566 2.50032 13.2463 3.1588 14.4184 4.33091C15.5905 5.50301 16.249 7.09272 16.249 8.75032C16.249 10.8027 16.6224 12.53 17.3279 13.7456ZM11.7661 15.6253H8.23177C8.36126 15.9905 8.60068 16.3066 8.91713 16.5301C9.23358 16.7537 9.61151 16.8737 9.99896 16.8737C10.3864 16.8737 10.7643 16.7537 11.0808 16.5301C11.3972 16.3066 11.6367 15.9905 11.7661 15.6253ZM16.249 14.3753C15.4177 12.948 14.999 11.0558 14.999 8.75032C14.999 7.42424 14.4722 6.15247 13.5345 5.21479C12.5968 4.27711 11.325 3.75032 9.99896 3.75032C8.67287 3.75032 7.4011 4.27711 6.46342 5.21479C5.52574 6.15247 4.99896 7.42424 4.99896 8.75032C4.99896 11.0566 4.57864 12.9488 3.74896 14.3753H16.249Z"
                        fill="#717171"
                      />
                    </svg>
                    <p className="text-lg font-medium mt-2">
                      Your notifications live here
                    </p>
                    <p className="text-sm text-white/70 mt-1">
                      No notifications yet
                    </p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {notifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`group flex items-start w-full gap-3 rounded-lg p-3 hover:bg-white/5 cursor-pointer relative transition-colors ${
                          !notification.is_read
                            ? "bg-blue-500/10 border-l-2 border-blue-500"
                            : ""
                        }`}
                        onClick={() =>
                          setIsShowMore((prev) => ({
                            ...prev,
                            [notification.id]: !prev[notification.id],
                          }))
                        }
                      >
                        <div
                          className={`flex h-8 w-8 basis-8 items-center justify-center rounded-full ${
                            !notification.is_read
                              ? "bg-blue-500/20"
                              : "bg-white/5"
                          }`}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={1.5}
                            stroke="currentColor"
                            className="size-4"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"
                            />
                          </svg>
                        </div>

                        <div className="text-start flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <p
                              className={`text-sm leading-normal ${
                                isShowMore[notification.id]
                                  ? ""
                                  : "line-clamp-3"
                              } ${!notification.is_read ? "font-medium" : ""}`}
                            >
                              {notification.text}
                            </p>
                            {!notification.is_read && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleMarkAsRead(notification.id);
                                }}
                                className="ml-2 text-blue-400 hover:text-blue-300 text-xs"
                                title="Mark as read"
                              >
                                ✓
                              </button>
                            )}
                          </div>
                          <p className="text-xs text-white/50 mt-1">
                            {formatRelativeTime(notification.created_at)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Footer with View All button */}
              {/* {notifications.length > 0 && ( */}
              <div className="border-t border-white/20 p-3">
                <button
                  onClick={() => {
                    setIsOpenNotification(false);
                    router.push("/notifications");
                  }}
                  className="w-full text-center text-sm text-blue-400 hover:text-blue-300 transition-colors"
                >
                  View all notifications
                </button>
              </div>
              {/* )} */}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
