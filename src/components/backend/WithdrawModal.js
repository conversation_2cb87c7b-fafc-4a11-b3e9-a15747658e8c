"use client";
import { Dialog, Transition } from "@headlessui/react";
import { Fragment, useState } from "react";

export default function WithdrawModal() {
  let [isOpen, setIsOpen] = useState(true);

  function closeModal() {
    setIsOpen(false);
  }

  function openModal() {
    setIsOpen(true);
  }

  return (
    <>
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/80" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-6 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-lg transform overflow-hidden rounded-2xl bg-[#141913] p-6 text-left align-middle shadow-xl transition-all">
                  <Dialog.Title
                    as="h3"
                    className="text-2xl inter font-medium leading-6 text-white"
                  >
                    Withdrawal Successful{" "}
                  </Dialog.Title>
                  <div className="mt-4">
                    <p className="text-base font-normal text-white/60">
                      Your withdrawal request has been successfully submitted.{" "}
                      <br />
                      You can track the status of your withdrawal in the <br />
                      Transactions section.
                    </p>
                  </div>

                  <div className="mt-5">
                    <div className="flex w-full rounded-md bg-[#FFFFFF0A] px-4 py-6 items-center text-sm font-medium border border-[#FFFFFF0A] justify-between">
                      <span className="inter font-normal text-xl text-white">
                        Updated Balance:
                      </span>
                      <span className="inter font-semibold text-xl text-[#95DA66]">
                        984 USD{" "}
                      </span>
                    </div>
                  </div>
                  <div className="mt-8 text-center">
                    <button
                      type="button"
                      className="inline-flex justify-center rounded-md border border-transparent bg-[#648A3A] px-8 py-2 text-base text-white font-semibold"
                      onClick={closeModal}
                    >
                      Got it, thanks!
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>

              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-lg transform overflow-hidden rounded-2xl bg-[#141913] p-6 text-left align-middle shadow-xl transition-all">
                  <Dialog.Title
                    as="h3"
                    className="text-xl inter font-medium leading-6 text-white"
                  >
                    Confirm Withdrawal{" "}
                  </Dialog.Title>
                  <div className="mt-4">
                    <p className="text-sm font-normal text-white/70">
                      Please confirm your withdrawal request with the following
                      details
                    </p>
                  </div>

                  <div className="mt-5">
                    <div className=" w-full rounded-md bg-[#FFFFFF0A] px-4 py-6 text-sm font-medium border border-[#FFFFFF0D]">
                      <p className="flex justify-between items-center mb-2">
                        <span className="inter font-normal text-sm text-white">
                          Amount:{" "}
                        </span>
                        <span className="inter font-semibold text-base text-white">
                          20.00 USD{" "}
                        </span>
                      </p>{" "}
                      <p className="flex justify-between items-center mb-2">
                        <span className="inter font-normal text-sm text-white">
                          Fee(0%):{" "}
                        </span>
                        <span className="inter font-semibold text-base text-white">
                          0.00 USD{" "}
                        </span>
                      </p>
                      <p className="flex justify-between items-center mb-2">
                        <span className="inter font-normal text-sm text-white">
                          Address Title:{" "}
                        </span>
                        <span className="inter font-semibold text-base text-white">
                          Binance{" "}
                        </span>
                      </p>
                      <p className="flex justify-between items-center mb-2">
                        <span className="inter font-normal text-sm text-white">
                          Crypto Address:
                        </span>
                        <span className="inter font-semibold text-base text-white">
                          6231dsfijksmldvxcjkmcjnmcx..
                        </span>
                      </p>
                      <p className="flex justify-between items-center mt-3 border-t border-[#666262] pt-2">
                        <span className="inter font-normal text-sm text-white">
                          You will receive:{" "}
                        </span>
                        <span className="inter font-semibold text-base text-[#95DA66]">
                          20.00 USDC{" "}
                        </span>
                      </p>
                    </div>
                  </div>
                  <div className="mt-5 text-center flex space-x-2 justify-end">
                    <button
                      type="button"
                      className="inline-flex justify-center rounded-full border border-transparent bg-[#FFFFFF1A] px-3 py-2 text-sm text-white font-semibold w-30 "
                      onClick={closeModal}
                    >
                      Cancel{" "}
                    </button>
                    <button
                      type="button"
                      className="inline-flex justify-center rounded-full border border-transparent bg-linear-to-r from-[#95DA66] to-[#648A3A] px-3 py-2 text-sm text-white font-semibold w-30"
                      onClick={closeModal}
                    >
                      Save{" "}
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}
