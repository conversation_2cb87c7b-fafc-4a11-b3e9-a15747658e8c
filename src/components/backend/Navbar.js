import Image from "next/image";
import React from "react";
import ProfileDropdown from "./ProfileDropdown";
import Link from "next/link";
import NotificationComponent from "./NotificationComponent";

const Navbar = () => {
  return (
    <>
      <div className="flex items-center sticky z-20 top-0 py-4 px-3 justify-between bg-[#1C1C1C]">
        <Link href="/dashboard">
          <Image
            src="/assets/backend_assets/images/Qacent-logo.svg"
            alt="Qacent Logo"
            width={100}
            height={100}
          />
        </Link>
        <ul className="flex items-center gap-4">
          <li className="archivo text-white flex items-center">
            <NotificationComponent />
          </li>
          <li className="archivo text-white flex items-center">
            <svg
              width={16}
              height={16}
              className="mr-2"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M8.00174 0.888916C4.08062 0.888916 0.890625 4.07892 0.890625 8.00003C0.890625 11.9212 4.08062 15.1111 8.00174 15.1111C11.9229 15.1111 15.1128 11.9212 15.1128 8.00003C15.1128 4.07892 11.9229 0.888916 8.00174 0.888916ZM7.58507 4.37937C6.95118 4.35213 6.32367 4.26305 5.7245 4.11819C5.82021 3.89141 5.92405 3.67364 6.03602 3.46725C6.49544 2.62068 7.05946 2.02661 7.58507 1.81144V4.37937ZM7.58507 5.21335V7.58336H4.98253C5.0192 6.65834 5.17686 5.74005 5.43761 4.90476C6.12858 5.0788 6.85395 5.18416 7.58507 5.21335ZM7.58507 8.41669V10.7867C6.85395 10.8158 6.12858 10.9213 5.43761 11.0953C5.17686 10.26 5.0192 9.34172 4.98253 8.41669H7.58507ZM7.58507 11.6206V14.1886C7.05946 13.9734 6.49544 13.3794 6.03602 12.5328C5.92405 12.3264 5.82021 12.1086 5.7245 11.8819C6.32367 11.737 6.95106 11.6479 7.58507 11.6206ZM8.4184 11.6206C9.05231 11.6479 9.67978 11.737 10.279 11.8819C10.1832 12.1086 10.0794 12.3264 9.96743 12.5328C9.50805 13.3794 8.94405 13.9734 8.4184 14.1886V11.6206ZM8.4184 10.7867V8.41669H11.0209C10.9842 9.34172 10.8266 10.26 10.5658 11.0953C9.87489 10.9213 9.14956 10.8158 8.4184 10.7867ZM8.4184 7.58336V5.21335C9.14956 5.18416 9.87489 5.0788 10.5658 4.90476C10.8266 5.74005 10.9842 6.65834 11.0209 7.58336H8.4184ZM8.4184 4.37937V1.81144C8.94405 2.02661 9.50805 2.62068 9.96743 3.46725C10.0794 3.67364 10.1832 3.89141 10.279 4.11819C9.67978 4.26305 9.05231 4.35213 8.4184 4.37937ZM10.6999 3.0698C10.4912 2.68524 10.2646 2.34628 10.0256 2.0571C10.8587 2.34161 11.6134 2.79679 12.248 3.38056C11.8861 3.57446 11.496 3.74286 11.0856 3.8836C10.9692 3.60083 10.8405 3.3288 10.6999 3.0698ZM5.3036 3.0698C5.16298 3.3288 5.03429 3.60083 4.91786 3.8836C4.50749 3.74286 4.11741 3.57436 3.75542 3.38056C4.39008 2.79679 5.14475 2.34161 5.97786 2.0571C5.73894 2.34628 5.51237 2.68514 5.3036 3.0698ZM4.63596 4.6668C4.35156 5.57663 4.18435 6.5685 4.14833 7.58336H1.73817C1.82736 6.22908 2.34744 4.99059 3.16298 4.00469C3.61556 4.26424 4.11122 4.48625 4.63596 4.6668ZM4.14833 8.41669C4.18435 9.43167 4.35156 10.4234 4.63596 11.3333C4.11122 11.5138 3.61556 11.7358 3.16298 11.9954C2.34744 11.0094 1.82736 9.77096 1.73817 8.41669H4.14833ZM4.91786 12.1165C5.03429 12.3992 5.16298 12.6712 5.3036 12.9302C5.51237 13.3148 5.73894 13.6538 5.97786 13.943C5.14475 13.6584 4.39008 13.2032 3.75553 12.6195C4.11729 12.4257 4.50749 12.2572 4.91786 12.1165ZM10.6999 12.9302C10.8405 12.6712 10.9692 12.3992 11.0856 12.1165C11.496 12.2572 11.8861 12.4257 12.248 12.6195C11.6134 13.2032 10.8587 13.6584 10.0256 13.943C10.2646 13.6538 10.4911 13.3149 10.6999 12.9302ZM11.3675 11.3333C11.6519 10.4234 11.8192 9.43158 11.8552 8.41669H14.2653C14.1761 9.77096 13.656 11.0094 12.8405 11.9954C12.3879 11.7358 11.8922 11.5138 11.3675 11.3333ZM11.8552 7.58336C11.8192 6.56838 11.6519 5.57663 11.3675 4.6668C11.8922 4.48625 12.3879 4.26424 12.8405 4.00469C13.656 4.99059 14.1761 6.22908 14.2653 7.58336H11.8552Z"
                fill="white"
              />
            </svg>
            English
          </li>
          <li>
            <ProfileDropdown />
          </li>
        </ul>
      </div>
    </>
  );
};

export default Navbar;
