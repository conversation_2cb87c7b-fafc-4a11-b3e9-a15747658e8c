"use client";

import { useEffect } from "react";
import { useApiLoader } from "@/utils/apiLoader";

/**
 * Component that initializes the API loader
 * This component doesn't render anything visible
 * It just initializes the API loader by calling the useApiLoader hook
 */
const ApiLoaderInitializer = () => {
  // Get the navigation context
  const navigation = useApiLoader();

  // Use an effect to ensure the navigation context is properly initialized
  // This ensures the global window object is updated even if the component re-renders
  useEffect(() => {
    if (typeof window !== "undefined") {
      window.__navigationContext = {
        startApiLoading: navigation.startApiLoading,
        endApiLoading: navigation.endApiLoading,
      };

      // Log to confirm initialization
      console.log("API Loader initialized");
    }
  }, [navigation]);

  // This component doesn't render anything
  return null;
};

export default ApiLoaderInitializer;
