"use client";

import React from "react";
import { usePathname } from "next/navigation";
import { useNavigation } from "@/context/NavigationContext";
import ShimmerLoader from "./ShimmerLoader";

const ConditionalNavigationLoader = () => {
  const pathname = usePathname();
  const { isNavigating, isApiLoading, isSidebarNavigation } = useNavigation();

  // Don't show the loader on the home page (/) path
  if (pathname === "/") {
    return null;
  }

  if (pathname.startsWith("/blog/")) {
    return null;
  }

  // Don't show loader for sidebar navigation to make it feel instant
  if (isSidebarNavigation) {
    return null;
  }

  // Show ShimmerLoader for API calls and non-sidebar navigation
  // This ensures fast sidebar navigation while still showing loaders for API calls
  return <ShimmerLoader isLoading={isNavigating || isApiLoading} />;
};

export default ConditionalNavigationLoader;
