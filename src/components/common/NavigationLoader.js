"use client";

import React from "react";
import { useNavigation } from "@/context/NavigationContext";
import ShimmerLoader from "./ShimmerLoader";

const NavigationLoader = () => {
  const { isNavigating, isApiLoading } = useNavigation();

  // Show ShimmerLoader for both navigation and API calls
  // The condition is simplified to show the loader for any navigation or API loading
  // This ensures the loader stays visible until API data is fully loaded
  return <ShimmerLoader isLoading={isNavigating || isApiLoading} />;
};

export default NavigationLoader;
