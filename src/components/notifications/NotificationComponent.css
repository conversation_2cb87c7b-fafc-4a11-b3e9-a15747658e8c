/* Main Notification Component Styles */
.notification-component {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Header */
.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.notification-title {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.unread-badge {
  background: #ef4444;
  color: white;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  min-width: 20px;
  text-align: center;
}

/* Filters */
.notification-filters {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #374151;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.mark-all-btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.mark-all-btn:hover:not(:disabled) {
  background: #2563eb;
}

.mark-all-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Content */
.notification-content {
  min-height: 200px;
}

/* States */
.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #6b7280;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state {
  color: #ef4444;
}

.retry-btn {
  margin-top: 12px;
  padding: 8px 16px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.retry-btn:hover {
  background: #dc2626;
}

/* Notification List */
.notification-list {
  padding: 0;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.2s ease;
  cursor: pointer;
}

.notification-item:hover {
  background: #f9fafb;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.unread {
  background: #eff6ff;
  border-left: 4px solid #3b82f6;
}

.notification-item.unread:hover {
  background: #dbeafe;
}

.notification-item .notification-content {
  flex: 1;
  min-width: 0;
  min-height: auto;
  padding: 0;
}

.notification-text {
  font-size: 16px;
  color: #374151;
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.notification-item.unread .notification-text {
  font-weight: 600;
  color: #111827;
}

.notification-time {
  font-size: 14px;
  color: #9ca3af;
}

.notification-actions {
  margin-left: 16px;
  display: flex;
  align-items: center;
}

.toggle-read-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  color: #6b7280;
  font-size: 16px;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-read-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.notification-item.unread .toggle-read-btn {
  color: #3b82f6;
}

.notification-item.unread .toggle-read-btn:hover {
  background: #dbeafe;
  color: #2563eb;
}

/* Pagination */
.notification-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.pagination-btn {
  padding: 8px 16px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.pagination-btn:disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

.pagination-info {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

/* Variants */
.notification-list {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.notification-page {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* Responsive Design */
@media (max-width: 768px) {
  .notification-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .notification-filters {
    justify-content: space-between;
  }
  
  .notification-item {
    padding: 16px;
  }
  
  .notification-text {
    font-size: 15px;
  }
  
  .notification-pagination {
    flex-direction: column;
    gap: 12px;
  }
}

/* Dark Theme */
.notification-component[data-theme="dark"] {
  background: #1f2937;
  color: #f9fafb;
}

.notification-component[data-theme="dark"] .notification-header {
  background: #111827;
  border-color: #374151;
}

.notification-component[data-theme="dark"] .notification-title {
  color: #f9fafb;
}

.notification-component[data-theme="dark"] .filter-select {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.notification-component[data-theme="dark"] .notification-item {
  border-color: #374151;
}

.notification-component[data-theme="dark"] .notification-item:hover {
  background: #374151;
}

.notification-component[data-theme="dark"] .notification-item.unread {
  background: #1e3a8a;
  border-left-color: #60a5fa;
}

.notification-component[data-theme="dark"] .notification-text {
  color: #d1d5db;
}

.notification-component[data-theme="dark"] .notification-item.unread .notification-text {
  color: #f9fafb;
}

.notification-component[data-theme="dark"] .notification-pagination {
  background: #111827;
  border-color: #374151;
}

.notification-component[data-theme="dark"] .pagination-btn {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

/* Auto Theme */
@media (prefers-color-scheme: dark) {
  .notification-component[data-theme="auto"] {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .notification-component[data-theme="auto"] .notification-header {
    background: #111827;
    border-color: #374151;
  }
  
  .notification-component[data-theme="auto"] .notification-title {
    color: #f9fafb;
  }
}
