/**
 * Notification Component Usage Examples
 * 
 * This file demonstrates various ways to use the notification components
 * in different scenarios and layouts.
 */

import React from 'react';
import { 
  NotificationComponent, 
  NotificationBell, 
  NotificationDropdown,
  notificationAPI 
} from './index';

// Example 1: Header with Notification Bell
export const HeaderExample = () => {
  return (
    <header className="app-header">
      <div className="header-left">
        <h1>My App</h1>
      </div>
      <div className="header-right">
        <NotificationBell 
          refreshInterval={30000}
          maxDisplayCount={99}
        />
        <div className="user-menu">
          {/* User menu items */}
        </div>
      </div>
    </header>
  );
};

// Example 2: Bell with Custom Navigation
export const CustomBellExample = () => {
  const handleNotificationClick = () => {
    // Navigate to notifications page using your router
    // For React Router: navigate('/notifications');
    // For Next.js: router.push('/notifications');
    window.location.href = '/notifications';
  };

  return (
    <NotificationBell 
      onNotificationClick={handleNotificationClick}
      showDropdown={false}
      className="custom-bell"
    />
  );
};

// Example 3: Full Notifications Page
export const NotificationsPageExample = () => {
  const handleNotificationClick = (notification) => {
    console.log('Notification clicked:', notification);
    // Handle notification click (e.g., navigate to related content)
  };

  const handleNotificationUpdate = (notificationId, isRead) => {
    console.log(`Notification ${notificationId} marked as ${isRead ? 'read' : 'unread'}`);
    // Optional: Update global state, show toast, etc.
  };

  return (
    <div className="notifications-page">
      <div className="page-header">
        <h1>Notifications</h1>
      </div>
      <div className="page-content">
        <NotificationComponent 
          variant="page"
          itemsPerPage={20}
          showFilters={true}
          showPagination={true}
          onNotificationClick={handleNotificationClick}
          onNotificationUpdate={handleNotificationUpdate}
          theme="auto"
        />
      </div>
    </div>
  );
};

// Example 4: Sidebar Notification List
export const SidebarExample = () => {
  return (
    <aside className="sidebar">
      <div className="sidebar-section">
        <h3>Recent Notifications</h3>
        <NotificationComponent 
          variant="list"
          itemsPerPage={5}
          showPagination={false}
          showFilters={false}
          refreshInterval={60000}
        />
      </div>
    </aside>
  );
};

// Example 5: Dashboard Widget
export const DashboardWidgetExample = () => {
  return (
    <div className="dashboard-widget">
      <div className="widget-header">
        <h3>Notifications</h3>
        <NotificationBell 
          showDropdown={true}
          maxDisplayCount={9}
          className="widget-bell"
        />
      </div>
      <div className="widget-content">
        <NotificationComponent 
          variant="list"
          itemsPerPage={3}
          showPagination={false}
          showFilters={false}
        />
      </div>
    </div>
  );
};

// Example 6: Mobile-Optimized Bell
export const MobileBellExample = () => {
  return (
    <div className="mobile-header">
      <button className="menu-toggle">☰</button>
      <h1>App</h1>
      <NotificationBell 
        className="mobile-bell"
        refreshInterval={45000}
      />
    </div>
  );
};

// Example 7: Custom Styled Component
export const CustomStyledExample = () => {
  return (
    <div className="custom-notification-container">
      <NotificationComponent 
        variant="page"
        className="custom-notifications"
        theme="dark"
        itemsPerPage={15}
      />
      
      <style jsx>{`
        .custom-notification-container {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 20px;
          border-radius: 12px;
        }
        
        .custom-notifications {
          border-radius: 8px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
      `}</style>
    </div>
  );
};

// Example 8: Using API Service Directly
export const APIServiceExample = () => {
  const [notifications, setNotifications] = React.useState([]);
  const [unreadCount, setUnreadCount] = React.useState(0);

  React.useEffect(() => {
    const fetchData = async () => {
      try {
        // Get notifications
        const notificationsResponse = await notificationAPI.getUserNotifications({
          page: 1,
          perPage: 10,
          is_read: 'false'
        });
        setNotifications(notificationsResponse.data?.data || []);

        // Get unread count
        const countResponse = await notificationAPI.getUnreadCount();
        setUnreadCount(countResponse.data?.unread_count || 0);
      } catch (error) {
        console.error('Error fetching notifications:', error);
      }
    };

    fetchData();
  }, []);

  const markAllAsRead = async () => {
    try {
      await notificationAPI.markAllAsRead();
      setUnreadCount(0);
      setNotifications(prev => 
        prev.map(n => ({ ...n, is_read: true }))
      );
    } catch (error) {
      console.error('Error marking all as read:', error);
    }
  };

  return (
    <div className="custom-notification-list">
      <div className="list-header">
        <h3>Notifications ({unreadCount} unread)</h3>
        {unreadCount > 0 && (
          <button onClick={markAllAsRead}>
            Mark all as read
          </button>
        )}
      </div>
      <div className="list-content">
        {notifications.map(notification => (
          <div 
            key={notification.id}
            className={`notification-item ${!notification.is_read ? 'unread' : ''}`}
          >
            <p>{notification.text}</p>
            <small>{new Date(notification.created_at).toLocaleDateString()}</small>
          </div>
        ))}
      </div>
    </div>
  );
};

// Example 9: Integration with React Router
export const RouterIntegrationExample = () => {
  // Assuming you're using React Router
  // import { useNavigate } from 'react-router-dom';
  // const navigate = useNavigate();

  const handleViewAll = () => {
    // navigate('/notifications');
    console.log('Navigate to notifications page');
  };

  const handleNotificationClick = (notification) => {
    // Navigate to related content based on notification type
    // navigate(`/orders/${notification.related_id}`);
    console.log('Navigate to related content:', notification);
  };

  return (
    <NotificationComponent 
      variant="bell"
      onViewAll={handleViewAll}
      onNotificationClick={handleNotificationClick}
    />
  );
};

// Example 10: Integration with State Management (Redux/Zustand)
export const StateManagementExample = () => {
  // Assuming you're using a state management library
  const handleNotificationUpdate = (notificationId, isRead) => {
    // Update global state
    // dispatch(updateNotificationStatus({ id: notificationId, isRead }));
    console.log('Update global state:', { notificationId, isRead });
  };

  return (
    <NotificationComponent 
      variant="page"
      onNotificationUpdate={handleNotificationUpdate}
    />
  );
};

// CSS Styles for Examples
export const ExampleStyles = () => (
  <style jsx global>{`
    .app-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 2rem;
      background: white;
      border-bottom: 1px solid #e5e7eb;
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .notifications-page {
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
    }

    .page-header {
      margin-bottom: 2rem;
    }

    .sidebar {
      width: 300px;
      background: #f9fafb;
      padding: 1rem;
      border-right: 1px solid #e5e7eb;
    }

    .sidebar-section {
      margin-bottom: 2rem;
    }

    .dashboard-widget {
      background: white;
      border-radius: 8px;
      padding: 1rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .widget-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .mobile-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1rem;
      background: white;
      border-bottom: 1px solid #e5e7eb;
    }

    .mobile-bell {
      padding: 0.5rem;
    }

    .custom-notification-list {
      background: white;
      border-radius: 8px;
      padding: 1rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid #e5e7eb;
    }

    .notification-item {
      padding: 0.75rem;
      border-bottom: 1px solid #f3f4f6;
      transition: background-color 0.2s;
    }

    .notification-item:hover {
      background: #f9fafb;
    }

    .notification-item.unread {
      background: #eff6ff;
      border-left: 3px solid #3b82f6;
    }

    @media (max-width: 768px) {
      .app-header {
        padding: 1rem;
      }
      
      .notifications-page {
        padding: 1rem;
      }
      
      .sidebar {
        width: 100%;
      }
    }
  `}</style>
);

// Export all examples
export default {
  HeaderExample,
  CustomBellExample,
  NotificationsPageExample,
  SidebarExample,
  DashboardWidgetExample,
  MobileBellExample,
  CustomStyledExample,
  APIServiceExample,
  RouterIntegrationExample,
  StateManagementExample,
  ExampleStyles,
};
