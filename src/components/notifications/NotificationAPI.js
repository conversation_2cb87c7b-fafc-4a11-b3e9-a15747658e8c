/**
 * Notification API Service
 * Handles all notification-related API calls for user notifications
 * Uses the existing apiService from the services
 */

import apiService from '@/services/api';

class NotificationAPI {
  constructor() {
    this.apiService = apiService;
  }

  /**
   * Get user notifications with pagination and filtering
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number (default: 1)
   * @param {number} params.perPage - Items per page (default: 10)
   * @param {string} params.is_read - Filter by read status ('true', 'false', or '')
   * @param {string} params.sortBy - Sort field (default: 'created_at')
   * @param {string} params.sortOrder - Sort order ('asc' or 'desc', default: 'desc')
   * @returns {Promise} User notifications with pagination
   */
  async getUserNotifications(params = {}) {
    return this.apiService.getNotifications(params);
  }

  /**
   * Get unread notification count for the authenticated user
   * @returns {Promise} Object with unread_count property
   */
  async getUnreadCount() {
    return this.apiService.getUnreadNotificationCount();
  }

  /**
   * Mark a notification as read or unread
   * @param {string|number} notificationId - Notification ID
   * @param {boolean} isRead - Whether to mark as read (true) or unread (false)
   * @returns {Promise} Updated notification object
   */
  async markAsRead(notificationId, isRead = true) {
    return this.apiService.markNotificationAsRead(notificationId, isRead);
  }

  /**
   * Bulk mark notifications as read or unread
   * @param {Array} notificationIds - Array of notification IDs
   * @param {boolean} isRead - Whether to mark as read (true) or unread (false)
   * @returns {Promise} Bulk update result
   */
  async bulkMarkAsRead(notificationIds, isRead = true) {
    return this.apiService.bulkMarkNotificationsAsRead(notificationIds, isRead);
  }

  /**
   * Get recent notifications (last 5 unread)
   * @returns {Promise} Recent notifications
   */
  async getRecentNotifications() {
    return this.getUserNotifications({
      page: 1,
      perPage: 5,
      is_read: 'false',
      sortBy: 'created_at',
      sortOrder: 'desc'
    });
  }

  /**
   * Mark all notifications as read
   * @returns {Promise} Bulk update result
   */
  async markAllAsRead() {
    // First get all unread notifications
    const unreadNotifications = await this.getUserNotifications({
      page: 1,
      perPage: 100, // Get a large number to cover most cases
      is_read: 'false'
    });

    if (unreadNotifications.status && unreadNotifications.data && unreadNotifications.data.data.length > 0) {
      const notificationIds = unreadNotifications.data.data.map(notification => notification.id);
      return this.bulkMarkAsRead(notificationIds, true);
    }

    return { status: true, message: 'No unread notifications to mark' };
  }
}

// Create and export a singleton instance
const notificationAPI = new NotificationAPI();
export default notificationAPI;
