/* Notification Dropdown Styles */
.notification-dropdown {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  width: 380px;
  max-height: 500px;
  overflow: hidden;
  z-index: 1000;
}

/* Header */
.notification-dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.notification-dropdown-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification-refresh-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.notification-refresh-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.notification-refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.refresh-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

.notification-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.notification-close-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.close-icon {
  width: 16px;
  height: 16px;
}

/* Actions */
.notification-actions {
  padding: 12px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.mark-all-read-btn {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 0;
  transition: color 0.2s ease;
}

.mark-all-read-btn:hover {
  color: #2563eb;
}

.mark-all-read-btn:disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

/* Content */
.notification-dropdown-content {
  max-height: 320px;
  overflow-y: auto;
}

/* Loading State */
.notification-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6b7280;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.notification-error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #ef4444;
  text-align: center;
}

.error-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 8px;
}

.retry-btn {
  margin-top: 12px;
  padding: 6px 12px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.retry-btn:hover {
  background: #dc2626;
}

/* Empty State */
.notification-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #9ca3af;
  text-align: center;
}

.empty-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 12px;
}

/* Notification List */
.notification-list {
  padding: 0;
}

/* Notification Item */
.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.notification-item:hover {
  background: #f9fafb;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.unread {
  background: #eff6ff;
  border-left: 3px solid #3b82f6;
}

.notification-item.unread:hover {
  background: #dbeafe;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-text {
  font-size: 14px;
  color: #374151;
  margin: 0 0 4px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-item.unread .notification-text {
  font-weight: 500;
  color: #111827;
}

.notification-time {
  font-size: 12px;
  color: #9ca3af;
}

.notification-actions {
  margin-left: 12px;
  display: flex;
  align-items: center;
}

.mark-read-btn,
.mark-unread-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.mark-read-btn:hover {
  background: #dcfce7;
  color: #16a34a;
}

.mark-unread-btn:hover {
  background: #dbeafe;
  color: #3b82f6;
}

.read-icon,
.unread-icon {
  width: 14px;
  height: 14px;
}

/* Footer */
.notification-dropdown-footer {
  padding: 12px 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  text-align: center;
}

.view-all-btn {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.view-all-btn:hover {
  background: #dbeafe;
  color: #2563eb;
}

/* Scrollbar Styling */
.notification-dropdown-content::-webkit-scrollbar {
  width: 6px;
}

.notification-dropdown-content::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.notification-dropdown-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.notification-dropdown-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Responsive Design */
@media (max-width: 480px) {
  .notification-dropdown {
    width: 320px;
  }
  
  .notification-item {
    padding: 12px 16px;
  }
  
  .notification-dropdown-header,
  .notification-actions,
  .notification-dropdown-footer {
    padding: 12px 16px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .notification-dropdown {
    background: #1f2937;
    border-color: #374151;
  }
  
  .notification-dropdown-header,
  .notification-actions,
  .notification-dropdown-footer {
    background: #111827;
    border-color: #374151;
  }
  
  .notification-dropdown-title {
    color: #f9fafb;
  }
  
  .notification-item {
    border-color: #374151;
  }
  
  .notification-item:hover {
    background: #374151;
  }
  
  .notification-item.unread {
    background: #1e3a8a;
    border-left-color: #60a5fa;
  }
  
  .notification-text {
    color: #d1d5db;
  }
  
  .notification-item.unread .notification-text {
    color: #f9fafb;
  }
}
