import React, { useState, useEffect, useRef } from 'react';
import notificationAPI from './NotificationAPI';
import NotificationDropdown from './NotificationDropdown';
import './NotificationBell.css';

/**
 * NotificationBell Component
 * Displays a bell icon with unread count and dropdown
 */
const NotificationBell = ({ 
  className = '',
  showDropdown = true,
  onNotificationClick = null,
  refreshInterval = 30000, // 30 seconds
  maxDisplayCount = 99
}) => {
  const [unreadCount, setUnreadCount] = useState(0);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const bellRef = useRef(null);
  const dropdownRef = useRef(null);

  // Fetch unread count
  const fetchUnreadCount = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await notificationAPI.getUnreadCount();
      setUnreadCount(response.data?.unread_count || 0);
    } catch (err) {
      console.error('Error fetching unread count:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle bell click
  const handleBellClick = () => {
    if (onNotificationClick) {
      onNotificationClick();
    } else if (showDropdown) {
      setIsDropdownOpen(!isDropdownOpen);
    }
  };

  // Handle click outside to close dropdown
  const handleClickOutside = (event) => {
    if (
      dropdownRef.current && 
      !dropdownRef.current.contains(event.target) &&
      bellRef.current && 
      !bellRef.current.contains(event.target)
    ) {
      setIsDropdownOpen(false);
    }
  };

  // Handle notification read/unread
  const handleNotificationUpdate = () => {
    fetchUnreadCount(); // Refresh count when notifications are updated
  };

  // Format display count
  const getDisplayCount = () => {
    if (unreadCount === 0) return null;
    if (unreadCount > maxDisplayCount) return `${maxDisplayCount}+`;
    return unreadCount.toString();
  };

  // Setup effects
  useEffect(() => {
    fetchUnreadCount();

    // Auto-refresh disabled to prevent unwanted behavior
    // If you need auto-refresh, uncomment the lines below:
    // const interval = setInterval(fetchUnreadCount, refreshInterval);

    // Add click outside listener
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      // clearInterval(interval); // Uncomment if auto-refresh is enabled
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [refreshInterval]);

  return (
    <div className={`notification-bell-container ${className}`}>
      {/* Bell Icon */}
      <button
        ref={bellRef}
        className={`notification-bell ${isLoading ? 'loading' : ''} ${error ? 'error' : ''}`}
        onClick={handleBellClick}
        title={`${unreadCount} unread notifications`}
        aria-label={`Notifications. ${unreadCount} unread.`}
      >
        {/* Bell SVG Icon */}
        <svg
          className="notification-bell-icon"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
          />
        </svg>

        {/* Unread Count Badge */}
        {unreadCount > 0 && (
          <span className="notification-badge" aria-hidden="true">
            {getDisplayCount()}
          </span>
        )}

        {/* Loading Indicator */}
        {isLoading && (
          <div className="notification-loading">
            <div className="loading-spinner"></div>
          </div>
        )}
      </button>

      {/* Dropdown */}
      {showDropdown && isDropdownOpen && (
        <div ref={dropdownRef} className="notification-dropdown-container">
          <NotificationDropdown
            onClose={() => setIsDropdownOpen(false)}
            onNotificationUpdate={handleNotificationUpdate}
          />
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="notification-error" title={error}>
          <svg
            className="error-icon"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
      )}
    </div>
  );
};

export default NotificationBell;
