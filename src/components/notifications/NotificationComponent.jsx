import React, { useState, useEffect, useCallback } from 'react';
import notificationAPI from './NotificationAPI';
import NotificationBell from './NotificationBell';
import './NotificationComponent.css';

/**
 * Main NotificationComponent
 * Provides a complete notification system with bell, dropdown, and full page view
 */
const NotificationComponent = ({
  // Display options
  variant = 'bell', // 'bell', 'list', 'page'
  className = '',
  
  // Bell specific props
  showDropdown = true,
  refreshInterval = 30000,
  maxDisplayCount = 99,
  
  // List specific props
  itemsPerPage = 10,
  showPagination = true,
  showFilters = true,
  
  // Callbacks
  onNotificationClick = null,
  onNotificationUpdate = null,
  onViewAll = null,
  
  // Styling
  theme = 'light', // 'light', 'dark', 'auto'
}) => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    is_read: '', // '', 'true', 'false'
    sortBy: 'created_at',
    sortOrder: 'desc'
  });

  // Fetch notifications based on variant
  const fetchNotifications = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (variant === 'bell') {
        // For bell, just get unread count
        const countResponse = await notificationAPI.getUnreadCount();
        setUnreadCount(countResponse.data?.unread_count || 0);
      } else {
        // For list/page, get paginated notifications
        const response = await notificationAPI.getUserNotifications({
          page: currentPage,
          perPage: itemsPerPage,
          ...filters
        });
        
        setNotifications(response.data?.data || []);
        setTotalPages(response.data?.last_page || 1);
        
        // Also get unread count
        const countResponse = await notificationAPI.getUnreadCount();
        setUnreadCount(countResponse.data?.unread_count || 0);
      }
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [variant, currentPage, itemsPerPage, filters]);

  // Handle notification update
  const handleNotificationUpdate = useCallback(async (notificationId, isRead) => {
    try {
      await notificationAPI.markAsRead(notificationId, isRead);
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, is_read: isRead }
            : notification
        )
      );
      
      // Update unread count
      setUnreadCount(prev => isRead ? Math.max(0, prev - 1) : prev + 1);
      
      // Call external callback
      if (onNotificationUpdate) {
        onNotificationUpdate(notificationId, isRead);
      }
    } catch (err) {
      console.error('Error updating notification:', err);
    }
  }, [onNotificationUpdate]);

  // Handle bulk mark as read
  const handleBulkMarkAsRead = useCallback(async (notificationIds, isRead = true) => {
    try {
      await notificationAPI.bulkMarkAsRead(notificationIds, isRead);
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notificationIds.includes(notification.id)
            ? { ...notification, is_read: isRead }
            : notification
        )
      );
      
      // Update unread count
      const affectedUnread = notifications.filter(n => 
        notificationIds.includes(n.id) && !n.is_read
      ).length;
      
      setUnreadCount(prev => 
        isRead ? Math.max(0, prev - affectedUnread) : prev + affectedUnread
      );
      
      // Call external callback
      if (onNotificationUpdate) {
        onNotificationUpdate(notificationIds, isRead);
      }
    } catch (err) {
      console.error('Error bulk updating notifications:', err);
    }
  }, [notifications, onNotificationUpdate]);

  // Handle filter change
  const handleFilterChange = useCallback((newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // Reset to first page
  }, []);

  // Handle page change
  const handlePageChange = useCallback((page) => {
    setCurrentPage(page);
  }, []);

  // Format relative time
  const formatRelativeTime = useCallback((dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    
    return date.toLocaleDateString();
  }, []);

  // Setup effects
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  // Auto-refresh for bell variant (disabled to prevent unwanted behavior)
  useEffect(() => {
    // Auto-refresh disabled to prevent issues
    // If you need auto-refresh, uncomment the lines below:
    // if (variant === 'bell' && refreshInterval > 0) {
    //   const interval = setInterval(fetchNotifications, refreshInterval);
    //   return () => clearInterval(interval);
    // }
  }, [variant, refreshInterval, fetchNotifications]);

  // Apply theme
  useEffect(() => {
    const element = document.querySelector(`.notification-component.${className}`);
    if (element) {
      element.setAttribute('data-theme', theme);
    }
  }, [theme, className]);

  // Render based on variant
  const renderContent = () => {
    switch (variant) {
      case 'bell':
        return (
          <NotificationBell
            className={className}
            showDropdown={showDropdown}
            onNotificationClick={onNotificationClick}
            refreshInterval={refreshInterval}
            maxDisplayCount={maxDisplayCount}
          />
        );

      case 'list':
      case 'page':
        return (
          <div className={`notification-component notification-${variant} ${className}`}>
            {/* Header */}
            <div className="notification-header">
              <h2 className="notification-title">
                Notifications
                {unreadCount > 0 && (
                  <span className="unread-badge">{unreadCount}</span>
                )}
              </h2>
              
              {/* Filters */}
              {showFilters && (
                <div className="notification-filters">
                  <select
                    value={filters.is_read}
                    onChange={(e) => handleFilterChange({ is_read: e.target.value })}
                    className="filter-select"
                  >
                    <option value="">All notifications</option>
                    <option value="false">Unread only</option>
                    <option value="true">Read only</option>
                  </select>
                  
                  <button
                    onClick={() => handleBulkMarkAsRead(
                      notifications.filter(n => !n.is_read).map(n => n.id)
                    )}
                    disabled={unreadCount === 0}
                    className="mark-all-btn"
                  >
                    Mark all as read
                  </button>
                </div>
              )}
            </div>

            {/* Content */}
            <div className="notification-content">
              {isLoading ? (
                <div className="loading-state">
                  <div className="loading-spinner"></div>
                  <span>Loading notifications...</span>
                </div>
              ) : error ? (
                <div className="error-state">
                  <span>Error: {error}</span>
                  <button onClick={fetchNotifications} className="retry-btn">
                    Retry
                  </button>
                </div>
              ) : notifications.length === 0 ? (
                <div className="empty-state">
                  <span>No notifications found</span>
                </div>
              ) : (
                <div className="notification-list">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`notification-item ${!notification.is_read ? 'unread' : ''}`}
                      onClick={() => onNotificationClick && onNotificationClick(notification)}
                    >
                      <div className="notification-content">
                        <p className="notification-text">{notification.text}</p>
                        <span className="notification-time">
                          {formatRelativeTime(notification.created_at)}
                        </span>
                      </div>
                      <div className="notification-actions">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleNotificationUpdate(notification.id, !notification.is_read);
                          }}
                          className="toggle-read-btn"
                          title={notification.is_read ? 'Mark as unread' : 'Mark as read'}
                        >
                          {notification.is_read ? '○' : '●'}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Pagination */}
            {showPagination && totalPages > 1 && (
              <div className="notification-pagination">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="pagination-btn"
                >
                  Previous
                </button>
                <span className="pagination-info">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="pagination-btn"
                >
                  Next
                </button>
              </div>
            )}
          </div>
        );

      default:
        return <div>Invalid notification variant: {variant}</div>;
    }
  };

  return renderContent();
};

export default NotificationComponent;
