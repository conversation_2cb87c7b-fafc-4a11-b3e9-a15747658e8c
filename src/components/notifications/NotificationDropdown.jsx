import React, { useState, useEffect } from 'react';
import notificationAPI from './NotificationAPI';
import './NotificationDropdown.css';

/**
 * NotificationDropdown Component
 * Displays a dropdown list of recent notifications
 */
const NotificationDropdown = ({ 
  onClose,
  onNotificationUpdate,
  maxItems = 5,
  showMarkAllRead = true,
  showViewAll = true,
  onViewAll = null
}) => {
  const [notifications, setNotifications] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isMarkingAllRead, setIsMarkingAllRead] = useState(false);

  // Fetch recent notifications
  const fetchNotifications = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await notificationAPI.getRecentNotifications();
      setNotifications(response.data?.data || []);
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle mark notification as read
  const handleMarkAsRead = async (notificationId, isRead = true) => {
    try {
      await notificationAPI.markAsRead(notificationId, isRead);
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, is_read: isRead }
            : notification
        )
      );
      
      // Notify parent component
      if (onNotificationUpdate) {
        onNotificationUpdate();
      }
    } catch (err) {
      console.error('Error updating notification:', err);
    }
  };

  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    try {
      setIsMarkingAllRead(true);
      await notificationAPI.markAllAsRead();
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, is_read: true }))
      );
      
      // Notify parent component
      if (onNotificationUpdate) {
        onNotificationUpdate();
      }
    } catch (err) {
      console.error('Error marking all as read:', err);
    } finally {
      setIsMarkingAllRead(false);
    }
  };

  // Handle view all notifications
  const handleViewAll = () => {
    if (onViewAll) {
      onViewAll();
    } else {
      // Default behavior - could navigate to notifications page
      console.log('View all notifications clicked');
    }
    onClose();
  };

  // Format relative time
  const formatRelativeTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    
    return date.toLocaleDateString();
  };

  // Setup effects
  useEffect(() => {
    fetchNotifications();
  }, []);

  return (
    <div className="notification-dropdown">
      {/* Header */}
      <div className="notification-dropdown-header">
        <h3 className="notification-dropdown-title">Notifications</h3>
        <div className="header-actions">
          <button
            className="notification-refresh-btn"
            onClick={fetchNotifications}
            disabled={isLoading}
            title="Refresh notifications"
          >
            <svg
              className={`refresh-icon ${isLoading ? 'spinning' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </button>
          <button
            className="notification-close-btn"
            onClick={onClose}
            aria-label="Close notifications"
          >
            <svg
              className="close-icon"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Actions */}
      {showMarkAllRead && notifications.some(n => !n.is_read) && (
        <div className="notification-actions">
          <button
            className="mark-all-read-btn"
            onClick={handleMarkAllAsRead}
            disabled={isMarkingAllRead}
          >
            {isMarkingAllRead ? 'Marking...' : 'Mark all as read'}
          </button>
        </div>
      )}

      {/* Content */}
      <div className="notification-dropdown-content">
        {isLoading ? (
          <div className="notification-loading-state">
            <div className="loading-spinner"></div>
            <span>Loading notifications...</span>
          </div>
        ) : error ? (
          <div className="notification-error-state">
            <svg
              className="error-icon"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span>Failed to load notifications</span>
            <button onClick={fetchNotifications} className="retry-btn">
              Retry
            </button>
          </div>
        ) : notifications.length === 0 ? (
          <div className="notification-empty-state">
            <svg
              className="empty-icon"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
              />
            </svg>
            <span>No notifications yet</span>
          </div>
        ) : (
          <div className="notification-list">
            {notifications.slice(0, maxItems).map((notification) => (
              <div
                key={notification.id}
                className={`notification-item ${!notification.is_read ? 'unread' : ''}`}
              >
                <div className="notification-content">
                  <p className="notification-text">{notification.text}</p>
                  <span className="notification-time">
                    {formatRelativeTime(notification.created_at)}
                  </span>
                </div>
                <div className="notification-actions">
                  {!notification.is_read ? (
                    <button
                      className="mark-read-btn"
                      onClick={() => handleMarkAsRead(notification.id, true)}
                      title="Mark as read"
                    >
                      <svg
                        className="read-icon"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </button>
                  ) : (
                    <button
                      className="mark-unread-btn"
                      onClick={() => handleMarkAsRead(notification.id, false)}
                      title="Mark as unread"
                    >
                      <svg
                        className="unread-icon"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <circle cx="12" cy="12" r="3" />
                      </svg>
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {showViewAll && notifications.length > 0 && (
        <div className="notification-dropdown-footer">
          <button
            className="view-all-btn"
            onClick={handleViewAll}
          >
            View all notifications
          </button>
        </div>
      )}
    </div>
  );
};

export default NotificationDropdown;
