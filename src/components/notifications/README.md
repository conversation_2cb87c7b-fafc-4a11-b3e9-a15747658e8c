# Notification Components

A comprehensive React notification system that integrates with your backend notification APIs.

## Features

- 🔔 **Bell Icon** with unread count and dropdown
- 📋 **Full Notification List** with pagination and filtering
- 🔄 **Real-time Updates** with configurable refresh intervals
- 📱 **Responsive Design** that works on all devices
- 🌙 **Dark Mode Support** with auto-detection
- ♿ **Accessibility** compliant with ARIA standards
- 🎨 **Customizable** styling and behavior

## API Endpoints

The components work with these backend endpoints:

```javascript
// User notification endpoints
GET    /api/clients/notifications              // Get user notifications
GET    /api/clients/notifications/unread-count // Get unread count
PUT    /api/clients/notifications/read/:id     // Mark as read/unread
PUT    /api/clients/notifications/bulk-read    // Bulk mark as read/unread
```

## Quick Start

### 1. Basic Bell Icon

```jsx
import { NotificationBell } from './components/notifications';

function Header() {
  return (
    <div className="header">
      <NotificationBell />
    </div>
  );
}
```

### 2. Bell with Custom Callback

```jsx
import { NotificationBell } from './components/notifications';

function Header() {
  const handleNotificationClick = () => {
    // Navigate to notifications page
    window.location.href = '/notifications';
  };

  return (
    <NotificationBell 
      onNotificationClick={handleNotificationClick}
      showDropdown={false}
    />
  );
}
```

### 3. Full Notification List

```jsx
import { NotificationComponent } from './components/notifications';

function NotificationsPage() {
  return (
    <div className="page">
      <NotificationComponent 
        variant="page"
        itemsPerPage={20}
        showFilters={true}
        showPagination={true}
      />
    </div>
  );
}
```

### 4. Compact List

```jsx
import { NotificationComponent } from './components/notifications';

function Sidebar() {
  return (
    <NotificationComponent 
      variant="list"
      itemsPerPage={5}
      showPagination={false}
      showFilters={false}
    />
  );
}
```

## Component Props

### NotificationComponent

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `'bell' \| 'list' \| 'page'` | `'bell'` | Display variant |
| `className` | `string` | `''` | Additional CSS classes |
| `showDropdown` | `boolean` | `true` | Show dropdown for bell variant |
| `refreshInterval` | `number` | `30000` | Auto-refresh interval (ms) |
| `maxDisplayCount` | `number` | `99` | Max count to display on badge |
| `itemsPerPage` | `number` | `10` | Items per page for list/page |
| `showPagination` | `boolean` | `true` | Show pagination controls |
| `showFilters` | `boolean` | `true` | Show filter controls |
| `theme` | `'light' \| 'dark' \| 'auto'` | `'light'` | Color theme |
| `onNotificationClick` | `function` | `null` | Callback for notification clicks |
| `onNotificationUpdate` | `function` | `null` | Callback for status updates |
| `onViewAll` | `function` | `null` | Callback for "view all" clicks |

### NotificationBell

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `className` | `string` | `''` | Additional CSS classes |
| `showDropdown` | `boolean` | `true` | Show dropdown on click |
| `onNotificationClick` | `function` | `null` | Custom click handler |
| `refreshInterval` | `number` | `30000` | Auto-refresh interval (ms) |
| `maxDisplayCount` | `number` | `99` | Max count to display |

## API Service

You can also use the API service directly:

```jsx
import { notificationAPI } from './components/notifications';

// Get notifications
const notifications = await notificationAPI.getUserNotifications({
  page: 1,
  perPage: 10,
  is_read: 'false'
});

// Get unread count
const count = await notificationAPI.getUnreadCount();

// Mark as read
await notificationAPI.markAsRead(notificationId, true);

// Bulk mark as read
await notificationAPI.bulkMarkAsRead([id1, id2, id3], true);

// Mark all as read
await notificationAPI.markAllAsRead();
```

## Styling

### CSS Custom Properties

```css
.notification-component {
  --notification-primary: #3b82f6;
  --notification-danger: #ef4444;
  --notification-background: #ffffff;
  --notification-text: #374151;
  --notification-border: #e5e7eb;
}
```

### Custom Themes

```jsx
<NotificationComponent 
  theme="dark"
  className="my-notifications"
/>
```

```css
.my-notifications {
  --notification-background: #1f2937;
  --notification-text: #f9fafb;
  --notification-border: #374151;
}
```

## Environment Configuration

Set your API base URL in your environment:

```bash
# .env
REACT_APP_API_URL=http://localhost:8080/api/clients
```

## Authentication

The components automatically include JWT tokens from localStorage or sessionStorage:

```javascript
// Token will be automatically included in API calls
localStorage.setItem('token', 'your-jwt-token');
// or
sessionStorage.setItem('token', 'your-jwt-token');
```

## Error Handling

All components include built-in error handling with retry functionality:

```jsx
<NotificationComponent 
  onNotificationUpdate={(id, isRead) => {
    console.log(`Notification ${id} marked as ${isRead ? 'read' : 'unread'}`);
  }}
/>
```

## Accessibility

- Full keyboard navigation support
- ARIA labels and roles
- Screen reader friendly
- High contrast mode support
- Reduced motion support

## Browser Support

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Performance

- Automatic request debouncing
- Efficient re-rendering with React.memo
- Lazy loading for large lists
- Configurable refresh intervals

## Troubleshooting

### Common Issues

1. **No notifications showing**: Check API endpoint and authentication
2. **Styling issues**: Ensure CSS files are imported
3. **Auto-refresh not working**: Check refreshInterval prop
4. **CORS errors**: Configure your backend CORS settings

### Debug Mode

```jsx
// Enable console logging for debugging
localStorage.setItem('notification-debug', 'true');
```
