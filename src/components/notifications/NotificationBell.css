/* Notification Bell Styles */
.notification-bell-container {
  position: relative;
  display: inline-block;
}

.notification-bell {
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.notification-bell:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.notification-bell:focus {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6;
}

.notification-bell.loading {
  opacity: 0.7;
}

.notification-bell.error {
  color: #ef4444;
}

.notification-bell-icon {
  width: 24px;
  height: 24px;
  transition: transform 0.2s ease;
}

.notification-bell:hover .notification-bell-icon {
  transform: scale(1.1);
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background-color: #ef4444;
  color: white;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
  line-height: 1;
  padding: 0 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

/* Loading Indicator */
.notification-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.loading-spinner {
  width: 12px;
  height: 12px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Icon */
.notification-error {
  position: absolute;
  top: -2px;
  right: -2px;
  color: #ef4444;
}

.error-icon {
  width: 12px;
  height: 12px;
}

/* Dropdown Container */
.notification-dropdown-container {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  margin-top: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .notification-bell {
    padding: 6px;
  }
  
  .notification-bell-icon {
    width: 20px;
    height: 20px;
  }
  
  .notification-badge {
    min-width: 16px;
    height: 16px;
    font-size: 10px;
    top: 1px;
    right: 1px;
  }
  
  .notification-dropdown-container {
    right: -100px;
    width: 300px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .notification-bell {
    color: #d1d5db;
  }
  
  .notification-bell:hover {
    background-color: #374151;
    color: #f9fafb;
  }
  
  .loading-spinner {
    border-color: #4b5563;
    border-top-color: #60a5fa;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .notification-bell {
    border: 1px solid currentColor;
  }
  
  .notification-badge {
    border: 2px solid white;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .notification-bell,
  .notification-bell-icon,
  .notification-badge,
  .loading-spinner {
    animation: none;
    transition: none;
  }
}
