import React from "react";

const VirtualSection = () => {
  return (
    <>
      <section className="min-h-screen py-10 lg:py-20">
        <div className="max-w-2xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-[52px] font-medium inter text-white mb-4">
              How Virtual Account Work{" "}
            </h2>
            <p className="inter font-normal inter text-[#FFFFFF] text-base lg:text-xl mt-2">
              Discover how a Virtual Bank Account can offer much more — from
              seamless global transactions to enhanced financial freedom.
            </p>
          </div>
        </div>
        <div className="max-w-6xl mx-auto py-8">
          <div className="relative">
            <img
              src="/assets/frontend_assets/secure.png"
              alt=""
              className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 -z-10"
            />
            <div className="timeline">
              <div className="timeline__container left ">
                <div
                  className="content border-[#FFFFFF0D] border bg-gradient-to-r from-[#648A3A0D] to-[#CDEAF71A] backdrop-blur-[3.9px]"
                  data-aos="fade-right"
                  data-aos-offset="300"
                  data-aos-easing="ease-in-sine"
                  data-aos-delay="50"
                  data-aos-duration="500"
                >
                  <div className="flex items-center space-x-4 h-full">
                    <img
                      src="/assets/frontend_assets/v1-icon.svg"
                      alt=""
                      className=""
                    />
                    <div className="max-w-xs">
                      <h2 className="inter font-medium text-xl lg:text-[26px] text-white capitalize">
                        Instant virtual account{" "}
                      </h2>
                      <p className="inter font-light text-base text-[#FFFFFFBF]">
                        Get instant access to your own virtual US bank account
                        with just a few clicks.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="timeline__container right">
                <div
                  className="content border-[#FFFFFF0D] border bg-gradient-to-r from-[#648A3A0D] to-[#CDEAF71A] backdrop-blur-[3.9px]"
                  data-aos="fade-left"
                  data-aos-offset="300"
                  data-aos-easing="ease-in-sine"
                  data-aos-delay="50"
                  data-aos-duration="500"
                >
                  <div className="flex items-center space-x-4 h-full">
                    <img
                      src="/assets/frontend_assets/v1-icon.svg"
                      alt=""
                      className=""
                    />
                    <div className="">
                      <h2 className="inter font-medium text-xl lg:text-[26px] text-white capitalize">
                        receive money{" "}
                      </h2>
                      <p className="inter font-light text-base text-[#FFFFFFBF]">
                        Receive USD via <span className="font-medium">ACH</span>
                        ,<span className="font-medium"> Wire</span>, or
                        <span className="font-medium">
                          {" "}
                          FedNow
                        </span> Seamlessly.{" "}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="timeline__container left">
                <div
                  className="content border-[#FFFFFF0D] border bg-gradient-to-r from-[#648A3A0D] to-[#CDEAF71A] backdrop-blur-[3.9px]"
                  data-aos="fade-right"
                  data-aos-offset="300"
                  data-aos-easing="ease-in-sine"
                  data-aos-delay="50"
                  data-aos-duration="500"
                >
                  <div className="flex items-center space-x-4 h-full">
                    <img
                      src="/assets/frontend_assets/v2-icon.svg"
                      alt=""
                      className=""
                    />
                    <div className="">
                      <h2 className="inter font-medium text-xl lg:text-[26px] text-white capitalize">
                        Auto USDC conversion{" "}
                      </h2>
                      <p className="inter font-light text-base text-[#FFFFFFBF]">
                        Automatic and hassle-free conversion to USDC{" "}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="timeline__container right">
                <div
                  className="content border-[#FFFFFF0D] border bg-gradient-to-r from-[#648A3A0D] to-[#CDEAF71A] backdrop-blur-[3.9px]"
                  data-aos="fade-left"
                  data-aos-offset="300"
                  data-aos-easing="ease-in-sine"
                  data-aos-delay="50"
                  data-aos-duration="500"
                >
                  <div className="flex items-center space-x-4 h-full">
                    <img
                      src="/assets/frontend_assets/v4-icon.svg"
                      alt=""
                      className=""
                    />
                    <div className="">
                      <h2 className="inter font-medium text-xl lg:text-[26px] text-white capitalize">
                        Transfer to crypto wallet{" "}
                      </h2>
                      <p className="inter font-light text-base text-[#FFFFFFBF]">
                        Withdraw funds straight to your crypto wallet with ease.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default VirtualSection;
