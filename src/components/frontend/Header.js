import Link from "next/link";
import React, { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { gsap } from "gsap";
import NotificationComponent from "../backend/NotificationComponent";
const Header = ({ openAuthModal }) => {
  const router = useRouter();

  const handleLaunchApp = () => {
    // Only run this code on the client side
    if (typeof window !== "undefined") {
      const token = localStorage.getItem("access_token");
      if (token) {
        // If token exists, redirect to dashboard
        router.push("/dashboard");
      } else {
        // If no token, open the login modal
        openAuthModal();
      }
    }
  };
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef(null);
  const buttonRef = useRef(null);

  useEffect(() => {
    const menu = menuRef.current;
    const button = buttonRef.current;

    if (isOpen) {
      // Open animation
      gsap.fromTo(
        menu,
        { opacity: 0, y: -20, display: "none" },
        {
          opacity: 1,
          y: 0,
          display: "block",
          duration: 0.3,
          ease: "power2.out",
        }
      );

      // Hamburger to X animation
      gsap.to(button.querySelectorAll("path"), {
        attr: { d: "M4 4L13 13M4 13L13 4" },
        duration: 0.3,
        ease: "power2.out",
      });
    } else {
      // Close animation
      gsap.to(menu, {
        opacity: 0,
        y: -20,
        duration: 0.2,
        ease: "power2.in",
        onComplete: () => {
          if (menu) menu.style.display = "none";
        },
      });

      // X to Hamburger animation
      gsap.to(button.querySelectorAll("path"), {
        attr: { d: "M1 1h15M1 7h15M1 13h15" },
        duration: 0.3,
        ease: "power2.out",
      });
    }
  }, [isOpen]);
  return (
    <>
      <nav className="w-full z-50 start-0 border-b-[0.5px] border-[#FFFFFF59] drop-shadow-[#000000A6] drop-shadow-sm bg-[#1C1C1C]">
        <div className="max-w-7xl flex flex-wrap items-center justify-between mx-auto py-6 px-4 relative">
          <Link
            href="/"
            className="flex items-center space-x-3 rtl:space-x-reverse"
          >
            <img
              src="/assets/frontend_assets/site_logo.svg"
              alt="Qacent Logo"
              className="lg:w-[150px] w-[100px]"
            />
          </Link>

          <div className="flex md:order-2 space-x-3 md:space-x-0 rtl:space-x-reverse">
            <button
              onClick={handleLaunchApp}
              type="button"
              className="text-white bg-gradient-to-r from-[#69E1A4] to-[#648A3A] hover:from-[#648A3A] hover:to-[#69E1A4] transition-all duration-300 inter font-semibold w-28 lg:min-w-[180px] rounded-full lg:text-base text-xs lg:px-4 py-1 lg:py-2 text-center cursor-pointer shadow-lg hover:shadow-[#69E1A4]/30"
            >
              Launch app
            </button>
            <button
              ref={buttonRef}
              onClick={() => setIsOpen(!isOpen)}
              type="button"
              className="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none transition-colors"
              aria-controls="navbar-sticky"
              aria-expanded={isOpen}
            >
              <svg
                className="w-5 h-5"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 17 14"
              >
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M1 1h15M1 7h15M1 13h15"
                />
              </svg>
            </button>
          </div>

          <div
            ref={menuRef}
            className="items-center justify-between block w-full md:hidden md:w-auto md:order-1 bg-[#1C1C1C] absolute md:relative top-full left-0 right-0 md:top-auto shadow-lg md:shadow-none"
            id="navbar-sticky"
          >
            <ul className="flex flex-col p-4 md:p-0 mt-4 text-[#777576] rounded-lg text-base font-semibold md:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0 inter">
              {["Features", "Partners", "About Us", "Support", "FAQ"].map(
                (item) => (
                  <li key={item}>
                    <a
                      href={`#${item.toLowerCase()}`}
                      className="block py-3 px-4 md:py-2 md:px-3 hover:text-[#648A3A] transition-colors duration-200"
                      onClick={() => setIsOpen(false)}
                    >
                      {item}
                    </a>
                  </li>
                )
              )}
            </ul>
          </div>
          <div
            className="items-center justify-between hidden w-full md:flex md:w-auto md:order-1 bg-[#1C1C1C] relative top-full z-50 shadow-lg md:shadow-none"
            id="navbar-sticky"
          >
            <ul className="flex flex-col p-4 md:p-0 mt-4 text-[#777576] rounded-lg text-base font-semibold md:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0 inter">
              {["Features", "Partners", "About Us", "Support", "FAQ"].map(
                (item) => (
                  <li key={item}>
                    <a
                      href={`#${item.toLowerCase()}`}
                      className="block py-3 px-4 md:py-2 md:px-3 hover:text-[#648A3A] transition-colors duration-200"
                    >
                      {item}
                    </a>
                  </li>
                )
              )}
            </ul>
          </div>
        </div>
      </nav>
    </>
  );
};

export default Header;
