import React from "react";
import Link from "next/link";
import Image from "next/image";

const RelatedBlogItem = ({ blog }) => {
  // Format the date
  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      day: "numeric",
      month: "long",
      year: "numeric",
    });
  };

  return (
    <li className="mb-10 block border-[#B1B1B166] border-b-2 pb-2 last:border-b-0">
      <Link href={`/blog/${blog.id}`} className="relative">
        <div className="relative w-full h-40 rounded-lg overflow-hidden shadow-[0_4px_20px_rgb(0,0,0,0.4)]">
          <Image
            src={
              blog?.image
                ? `${process.env.NEXT_PUBLIC_MEDIA_URL}${blog.image}`
                : "/assets/frontend_assets/blog-card-1.png"
            }
            alt={blog.title || "Blog image"}
            fill
            style={{ objectFit: "cover" }}
            sizes="(max-width: 768px) 100vw, 300px"
            className="hover:scale-105 transition-transform duration-500"
          />
        </div>
        <div className="py-4 relative">
          {blog.created_at && (
            <p className="text-[#648A3A] poppins font-semibold text-sm mb-2">
              {formatDate(blog.created_at)}
            </p>
          )}
          <div className="flex justify-between items-start">
            <h4 className="inter max-w-[200px] font-semibold text-[22px] leading-tight tracking-[-3%] text-white">
              {blog.title}
            </h4>
            <svg
              width={24}
              height={24}
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M7 17L17 7M17 7H7M17 7V17"
                stroke="white"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>

          <p
            className="text-[#98989A] text-lg inter font-normal tracking-[-3%] mt-2"
            dangerouslySetInnerHTML={{ __html: blog?.description || "" }}
          />

          {blog.author && (
            <p className="text-[#98989A] text-sm inter font-normal mt-2">
              By <span className="text-white">{blog.author}</span>
            </p>
          )}
        </div>
      </Link>
    </li>
  );
};

export default RelatedBlogItem;
