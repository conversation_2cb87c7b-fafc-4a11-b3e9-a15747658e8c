"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import Blog<PERSON>ard from "./BlogCard";
import apiService from "@/services/api";

const BlogSection = () => {
  const [blogs, setBlogs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        setIsLoading(true);
        const response = await apiService.getRecentBlogs();

        if (response.status && response.data) {
          setBlogs(response.data);
        } else {
          setError("Failed to fetch blogs");
        }
      } catch (err) {
        console.error("Error fetching blogs:", err);
        setError("An error occurred while fetching blogs");
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlogs();
  }, []);

  // Fallback data in case API fails
  const fallbackData = [
    {
      id: "1",
      title: "Getting Started with Crypto Investments",
      image: "/assets/frontend_assets/blog-card-1.png",
      description:
        "Learn the basics of cryptocurrency investments and how to build a diversified portfolio...",
      created_at: "2023-06-20T14:53:10.000Z",
      author: "Alex Johnson",
    },
    {
      id: "2",
      title: "Understanding Blockchain Technology",
      image: "/assets/frontend_assets/blog-card-1.png",
      description:
        "A comprehensive guide to understanding blockchain technology and its applications...",
      created_at: "2023-06-21T14:53:10.000Z",
      author: "Jane Smith",
    },
    {
      id: "3",
      title: "Latest Cryptocurrency Trends",
      image: "/assets/frontend_assets/blog-card-1.png",
      description:
        "Exploring the latest trends in cryptocurrency markets and what they mean for investors...",
      created_at: "2023-06-22T14:53:10.000Z",
      author: "John Doe",
    },
  ];

  // Use blogs from API or fallback data if API fails
  // Check if blogs is an array or if it has a data property that's an array
  const displayBlogs = Array.isArray(blogs)
    ? blogs
    : blogs && Array.isArray(blogs.data)
    ? blogs.data
    : fallbackData;

  return (
    <>
      <section className="bg-[#1C1C1C] py-10 relative">
        <div className="absolute left-0 w-[400px] h-[400px]">
          <Image
            src="/assets/frontend_assets/blog-Ellipse.png"
            alt="Background decoration"
            fill
            style={{ objectFit: "contain" }}
            priority
          />
        </div>
        <div className="max-w-7xl mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <div className="text-center mb-20">
              <h1 className="text-3xl lg:text-[52px] font-medium inter text-white mb-4">
                Blogs & Articles
              </h1>
              <p className="inter font-normal inter text-[#FFFFFFBF] text-base lg:text-xl mt-2">
                Discover who we are, what we stand for, and how our mission
                drives us to deliver excellence every step of the way.
              </p>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#648A3A]"></div>
            </div>
          ) : error ? (
            <div className="text-center text-red-500 py-10">
              <p>{error}</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-16">
              {displayBlogs.map((item) => (
                <BlogCard key={item.id} item={item} />
              ))}
            </div>
          )}
        </div>
        <div className="absolute right-0 bottom-0 w-[400px] h-[400px]">
          <Image
            src="/assets/frontend_assets/blog-Ellipse-2.png"
            alt="Background decoration"
            fill
            style={{ objectFit: "contain" }}
          />
        </div>
      </section>
    </>
  );
};

export default BlogSection;
