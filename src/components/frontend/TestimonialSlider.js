"use client";
import React, { useRef, useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { EffectCoverflow, FreeMode } from "swiper/modules";
import { gsap } from "gsap";
import "swiper/css";
import "swiper/css/effect-coverflow";
import "swiper/css/free-mode";
import apiService from "@/services/api";
import Image from "next/image";
const TestimonialSlider = () => {
  const [testimonials, setTestimonials] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const swiperRef = useRef(null);
  const animationRef = useRef(null);

  useEffect(() => {
    const fetchTestimonials = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await apiService.getTestimonials();

        if (response.status && response.data) {
          // Transform API data to match component structure

          setTestimonials(response?.data);
        } else {
          setError("Failed to load testimonials");
        }
      } catch (err) {
        console.error("Error fetching testimonials:", err);
        setError("An error occurred while loading testimonials");
      } finally {
        setIsLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  // console.log("Testimonials:", testimonials);

  // Calculate initial transforms
  const getSlideTransform = (index) => {
    const x = 76.2353 + -48 * index;
    const rotate = -2.94118 + 5 * index;
    return { x, rotate };
  };

  // Animate slides to their positions
  const animateSlides = (targetIndex) => {
    const swiper = swiperRef.current?.swiper;
    if (!swiper) return;

    // Kill any existing animations
    if (animationRef.current) {
      animationRef.current.kill();
    }

    const slides = swiper.slides;
    const currentIndex = swiper.activeIndex;
    const direction = targetIndex > currentIndex ? 1 : -1;

    // Create GSAP timeline for smooth coordinated animations
    animationRef.current = gsap.timeline({
      defaults: { duration: 0.6, ease: "power3.out" },
    });

    slides.forEach((slide, index) => {
      const { x, rotate } = getSlideTransform(index - targetIndex);

      animationRef.current.to(
        slide,
        {
          x: x,
          rotateZ: rotate,
          ease: "power3.out",
          overwrite: "auto",
        },
        0
      );
    });

    // Update swiper's internal state after animation
    animationRef.current.eventCallback("onComplete", () => {
      swiper.slideTo(targetIndex, 0);
    });
  };

  // Handle swipe/transition
  const handleTransition = () => {
    const swiper = swiperRef.current?.swiper;
    if (!swiper) return;

    const targetIndex = swiper.activeIndex;
    animateSlides(targetIndex);
  };

  useEffect(() => {
    const swiper = swiperRef.current?.swiper;
    if (swiper) {
      // Initialize positions
      const initialIndex = 2; // Start with 3rd slide centered
      const slides = swiper.slides; // Define slides here

      slides.forEach((slide, index) => {
        const { x, rotate } = getSlideTransform(index - initialIndex);
        gsap.set(slide, {
          x: x,
          rotateZ: rotate,
        });
      });

      // Set up event listeners
      swiper.on("slideChange", handleTransition);
      swiper.on("transitionEnd", handleTransition);

      return () => {
        swiper.off("slideChange", handleTransition);
        swiper.off("transitionEnd", handleTransition);
        if (animationRef.current) {
          animationRef.current.kill();
        }
      };
    }
  }, []);

  return (
    <div className="relative">
      <div className="testimonial_slider py-20">
        <div className="max-w-3xl mx-auto px-4">
          <div className="text-center mb-20 max-w-4xl mx-auto">
            <h1 className="text-3xl lg:text-[52px] font-medium inter text-white mb-4">
              TESTIMONIALS{" "}
            </h1>
            <p className="inter font-normal inter text-[#FFFFFFBF] text-base lg:text-xl mt-2">
              Hear directly from our users and partners as they share their
              experiences, success stories, and trust in our platform on their
              journey
            </p>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center items-center py-20">
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-[#95DA66] mb-4"></div>
              <p className="text-white text-lg inter">
                Loading testimonials...
              </p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && !isLoading && (
          <div className="text-center py-20">
            <p className="text-red-500 text-lg mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-2 bg-[#95DA66] text-white rounded-lg hover:bg-[#7BC142] transition-colors inter font-medium"
            >
              Try Again
            </button>
          </div>
        )}

        {/* Empty State */}
        {!isLoading && !error && testimonials.length === 0 && (
          <div className="text-center py-20">
            <p className="text-white text-lg inter">
              No testimonials available at the moment.
            </p>
          </div>
        )}

        {/* Testimonials Swiper - Only show when data is loaded */}
        {!isLoading && !error && testimonials.length > 0 && (
          <Swiper
            ref={swiperRef}
            grabCursor={true}
            centeredSlides={true}
            slidesPerView={"auto"}
            initialSlide={1}
            freeMode={{
              enabled: true,
              momentumRatio: 0.5,
              momentumBounceRatio: 0.5,
              sticky: true,
            }}
            modules={[FreeMode]}
            className="custom-swiper"
            resistanceRatio={0.7}
            onSlideChange={handleTransition}
            onTransitionEnd={handleTransition}
            loop={true}
          >
            {testimonials.map((testimonial, index) => (
              <SwiperSlide
                key={index}
                className="!w-[340px] !h-[270px]"
                style={{
                  backfaceVisibility: "hidden",
                  transformStyle: "preserve-3d",
                }}
              >
                <div
                  className={`card2 p-8 rounded-3xl h-full flex flex-col shadow-xl bg-gradient-to-r from-[#648A3A0D] to-[#95DA661A] relative text-white`}
                  style={{
                    backgroundColor: testimonial?.bg_color,
                  }}
                >
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 rounded-full overflow-hidden">
                      <Image
                        src={
                          testimonial?.photo
                            ? `${process.env.NEXT_PUBLIC_MEDIA_URL}${testimonial.photo}`
                            : testimonial?.avatar ||
                              "/assets/frontend_assets/t1.png"
                        }
                        width={150}
                        height={150}
                        alt={testimonial?.name || "testimonial image"}
                        className="w-full h-full object-cover rounded-full"
                      />
                    </div>
                    <div>
                      <h3 className="font-medium text-xl inter my-2 text-white">
                        {testimonial?.name}
                      </h3>
                      <p className="text-base poppins text-white opacity-60 font-normal italic">
                        {testimonial.designation}
                      </p>
                    </div>
                  </div>
                  <p className="text-base poppins text-white opacity-60 font-normal italic">
                    {testimonial.description &&
                    testimonial.description.split(" ").length > 200
                      ? `${testimonial.description
                          .split(" ")
                          .slice(0, 200)
                          .join(" ")}...`
                      : testimonial.description}
                  </p>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        )}
      </div>
    </div>
  );
};

export default TestimonialSlider;
