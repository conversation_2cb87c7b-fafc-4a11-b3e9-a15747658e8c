"use client";

import {
  createContext,
  useContext,
  useState,
  useEffect,
  Suspense,
  useCallback,
  useRef,
} from "react";
import { usePathname, useSearchParams } from "next/navigation";

// Create the navigation context
const NavigationContext = createContext();

// Client component that uses useSearchParams
const RouteChangeDetector = ({ onRouteChange }) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    onRouteChange(pathname, searchParams);
  }, [pathname, searchParams, onRouteChange]);

  return null;
};

// Navigation provider component
export function NavigationProvider({ children }) {
  const [isNavigating, setIsNavigating] = useState(false);
  const [isSidebarNavigation, setSidebarNavigation] = useState(false);
  const [isApiLoading, setIsApiLoading] = useState(false);
  const [apiLoadingEndpoints, setApiLoadingEndpoints] = useState(new Set());
  const timerRef = useRef(null);

  // Handle route changes
  const handleRouteChange = useCallback(() => {
    // Show loader for all navigation, but mark sidebar navigation specially
    setIsNavigating(true);

    // We don't automatically hide the loader after a timeout anymore
    // Instead, we'll keep it visible until all API calls are complete
    // This ensures the loader stays visible during the entire loading process

    // We'll only reset the navigation state when the page is fully loaded
    // This happens in the useEffect below that watches isApiLoading

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    };
  }, []);

  // Function to start API loading for a specific endpoint
  const startApiLoading = useCallback((endpoint) => {
    setIsApiLoading(true);
    setApiLoadingEndpoints((prev) => {
      const newSet = new Set(prev);
      newSet.add(endpoint);
      return newSet;
    });
  }, []);

  // Function to end API loading for a specific endpoint
  const endApiLoading = useCallback((endpoint) => {
    setApiLoadingEndpoints((prev) => {
      const newSet = new Set(prev);
      newSet.delete(endpoint);
      // Only set isApiLoading to false if there are no more loading endpoints
      if (newSet.size === 0) {
        // Immediate update for better responsiveness
        setIsApiLoading(false);
      }
      return newSet;
    });
  }, []);

  // Effect to coordinate navigation and API loading states
  useEffect(() => {
    // For sidebar navigation, hide loader immediately for better UX
    // For other navigation, wait for API loading to complete
    if (!isApiLoading && isNavigating) {
      if (isSidebarNavigation) {
        // Immediate response for sidebar navigation
        setIsNavigating(false);
        setSidebarNavigation(false);
      } else {
        // Small delay for other navigation types
        timerRef.current = setTimeout(() => {
          setIsNavigating(false);
          setSidebarNavigation(false);
        }, 100);
      }
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [isApiLoading, isNavigating, isSidebarNavigation]);

  // Context value
  const value = {
    isNavigating,
    setIsNavigating,
    isSidebarNavigation,
    setSidebarNavigation,
    isApiLoading,
    startApiLoading,
    endApiLoading,
    apiLoadingEndpoints,
  };

  return (
    <NavigationContext.Provider value={value}>
      <Suspense fallback={null}>
        <RouteChangeDetector onRouteChange={handleRouteChange} />
      </Suspense>
      {children}
    </NavigationContext.Provider>
  );
}

// Custom hook to use navigation context
export function useNavigation() {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error("useNavigation must be used within a NavigationProvider");
  }
  return context;
}
