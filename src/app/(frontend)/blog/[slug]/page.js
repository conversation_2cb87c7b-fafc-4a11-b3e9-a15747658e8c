"use client";
import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { useParams } from "next/navigation";
import apiService from "@/services/api";
import RelatedBlogItem from "@/components/frontend/RelatedBlogItem";

const BlogDetails = () => {
  const { slug } = useParams();
  const [blog, setBlog] = useState(null);
  const [relatedBlogs, setRelatedBlogs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchBlogDetails = async () => {
      if (!slug) return;

      try {
        setIsLoading(true);
        const response = await apiService.getBlogById(slug);

        if (response.status && response.data) {
          // The API returns data in the format { data: { blog: {...}, related_blogs: [...] } }
          if (response.data.blog) {
            setBlog(response.data.blog);
          }

          if (
            response.data.related_blogs &&
            Array.isArray(response.data.related_blogs)
          ) {
            setRelatedBlogs(response.data.related_blogs);
          }
        } else {
          setError("Failed to fetch blog details");
        }
      } catch (err) {
        console.error("Error fetching blog details:", err);
        setError("An error occurred while fetching blog details");
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlogDetails();
  }, [slug]);

  // Format the date
  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      day: "numeric",
      month: "long",
      year: "numeric",
    });
  };

  // Get the image URL (handle both relative and absolute paths)
  const getImageUrl = (imagePath) => {
    if (!imagePath) return "/assets/frontend_assets/blog-card-1.png";
    return imagePath.startsWith("http")
      ? imagePath
      : `${process.env.NEXT_PUBLIC_API_BASE_URL}${imagePath}`;
  };

  // Fallback data in case API fails
  const fallbackBlog = {
    id: slug || "1",
    title: "Latest Cryptocurrency Trends",
    image: "/assets/frontend_assets/blog-card-1.png",
    description:
      "Exploring the latest trends in cryptocurrency markets and what they mean for investors. This comprehensive analysis looks at market movements, regulatory developments, and technological advancements that are shaping the future of digital currencies...",
    created_at: "2023-06-22T14:53:10.000Z",
    author: "John Doe",
  };

  const fallbackRelatedBlogs = [
    {
      id: "2",
      title: "Understanding Blockchain Technology",
      image: "/assets/frontend_assets/blog-card-1.png",
      description:
        "A comprehensive guide to understanding blockchain technology and its applications...",
      created_at: "2023-06-21T14:53:10.000Z",
      author: "Jane Smith",
    },
    {
      id: "3",
      title: "Getting Started with Crypto Investments",
      image: "/assets/frontend_assets/blog-card-1.png",
      description:
        "Learn the basics of cryptocurrency investments and how to build a diversified portfolio...",
      created_at: "2023-06-20T14:53:10.000Z",
      author: "Alex Johnson",
    },
  ];

  // Use blog from API or fallback data if API fails
  const displayBlog = blog || fallbackBlog;
  const displayRelatedBlogs =
    relatedBlogs.length > 0 ? relatedBlogs : fallbackRelatedBlogs;
  return (
    <>
      <div className="">
        <div className="max-w-7xl mx-auto px-4">
          <nav className="flex pt-10">
            <ol className="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
              <li className="inline-flex items-center">
                <Link
                  href="/"
                  className="inline-flex items-center text-sm font-medium text-white"
                >
                  <svg
                    width={18}
                    height={15}
                    viewBox="0 0 18 15"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.0013 2.74167L13.168 6.49167V13H11.5013V8H6.5013V13H4.83464V6.49167L9.0013 2.74167ZM9.0013 0.5L0.667969 8H3.16797V14.6667H8.16797V9.66667H9.83464V14.6667H14.8346V8H17.3346L9.0013 0.5Z"
                      fill="white"
                      fillOpacity="0.72"
                    />
                  </svg>
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg
                    className="w-3 h-3 text-gray-400 mx-1"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 6 10"
                  >
                    <path
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="m1 9 4-4-4-4"
                    />
                  </svg>
                  <Link
                    href="/blog"
                    className="ml-1 text-sm font-medium text-white md:ml-2"
                  >
                    Blogs
                  </Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <svg
                    className="w-3 h-3 text-gray-400 mx-1"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 6 10"
                  >
                    <path
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="m1 9 4-4-4-4"
                    />
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">
                    {displayBlog.title}
                  </span>
                </div>
              </li>
            </ol>
          </nav>

          {isLoading ? (
            <div className="flex justify-center items-center h-96">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#648A3A]"></div>
            </div>
          ) : error ? (
            <div className="text-center text-red-500 py-10">
              <p>{error}</p>
            </div>
          ) : (
            <div className="pb-20 pt-10">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-20">
                <ul className="col-span-1 order-2 md:order-1">
                  {displayRelatedBlogs.map((relatedBlog) => (
                    <RelatedBlogItem key={relatedBlog.id} blog={relatedBlog} />
                  ))}
                </ul>
                <div className="col-span-2 order-1 md:order-2">
                  <div className="mb-6">
                    <p className="text-[#648A3A] poppins font-semibold text-sm mb-2">
                      {formatDate(displayBlog.created_at)}
                    </p>
                    <h3 className="text-4xl font-bold inter text-white mb-2">
                      {displayBlog.title}
                    </h3>
                    {displayBlog.author && (
                      <p className="text-xl font-normal inter text-white">
                        Published by:{" "}
                        <span className="font-semibold">
                          {displayBlog.author}
                        </span>
                      </p>
                    )}
                  </div>
                  <div className="dangerous__html">
                    <div className="relative w-full h-80 mb-6 rounded-lg overflow-hidden shadow-[0_8px_30px_rgb(0,0,0,0.5)]">
                      <Image
                        src={
                          displayBlog?.image
                            ? `${process.env.NEXT_PUBLIC_MEDIA_URL}${displayBlog.image}`
                            : "/assets/frontend_assets/blog-card-1.png"
                        }
                        alt={displayBlog.title}
                        fill
                        style={{ objectFit: "cover" }}
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 800px"
                        priority
                        className="hover:scale-105 transition-transform duration-500"
                      />
                    </div>
                    <div className="text-white space-y-4">
                      <p
                        className="text-[#98989A] text-lg inter font-normal tracking-[-3%] mt-2"
                        dangerouslySetInnerHTML={{
                          __html: displayBlog?.description || "",
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default BlogDetails;
