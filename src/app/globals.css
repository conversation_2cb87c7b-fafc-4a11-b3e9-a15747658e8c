@import url("https://fonts.googleapis.com/css2?family=Archivo:ital,wght@0,100..900;1,100..900&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Work+Sans:ital,wght@0,100..900;1,100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Krona+One&display=swap");
/* @import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap'); */

@import "tailwindcss";
html {
  scroll-behavior: smooth;
}
:root {
  --color-bg1: #1d4503;
  --color-bg2: #1d4503;
  --color1: #1d4503;
  --color2: 221, 74, 155;
  --color3: 100, 120, 155;
  --color4: 100, 50, 50;
  --color5: 80, 80, 50;
  --color-interactive: 100, 100, 155;
  --circle-size: 80%;
  --blending: hard-light;
}
.archivo {
  font-family: "Archivo", sans-serif;
}

.inter {
  font-family: "Inter", sans-serif;
}

.workSans {
  font-family: "Work Sans", sans-serif;
}

.kronaOne {
  font-family: "Krona One", sans-serif;
}
.poppins {
  font-family: "Poppins", sans-serif;
}

.gradient_text {
  background: linear-gradient(to right, #69e1a4, #648a3a);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  cursor: pointer;
  border-radius: 4px;
  margin-right: 2px;
  opacity: 0.6;
  color-scheme: dark;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
}

/* Shimmer animation */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 1.5s infinite linear;
}

/* Pulse animation for the loader */
@keyframes pulse-subtle {
  0%,
  100% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }
}

.animate-pulse {
  animation: pulse-subtle 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Slower spin animation */
.animate-spin-slow {
  animation: spin 3s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Bounce animations with delays */
@keyframes bounce-delay {
  0%,
  100% {
    transform: translateY(0);
    opacity: 0.5;
  }

  50% {
    transform: translateY(-3px);
    opacity: 1;
  }
}

.animate-bounce-delay-1 {
  animation: bounce-delay 1.2s infinite ease-in-out;
}

.animate-bounce-delay-2 {
  animation: bounce-delay 1.2s infinite ease-in-out 0.2s;
}

.animate-bounce-delay-3 {
  animation: bounce-delay 1.2s infinite ease-in-out 0.4s;
}

/* Cursor styles */
.cursor-pointer {
  cursor: pointer;
}

.faq-item {
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 1rem;
}

.faq-item input[type="radio"] {
  position: absolute;
  opacity: 0;
  z-index: -1;
}

.faq-content {
  transition: all 0.3s ease;
}

.faq-item input[type="radio"]:checked ~ .faq-content {
  border: 1px solid #9ed95b;
  border-radius: 0.5rem;
}
.faq-item input[type="radio"]:checked ~ .faq-content .faq-label {
  border-bottom: 1px solid #ffffff33;
  margin-bottom: 20px;
}

.faq-label {
  @apply p-3 lg:p-5 flex items-center justify-between w-full cursor-pointer text-white font-medium text-left;
}

.faq-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  transition: all 0.3s ease;
}

.faq-item input[type="radio"]:checked ~ .faq-content .plus-icon {
  display: none;
}

.faq-item input[type="radio"]:not(:checked) ~ .faq-content .minus-icon {
  display: none;
}

.faq-answer-container {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.faq-item input[type="radio"]:checked ~ .faq-content .faq-answer-container {
  max-height: 500px;
}

.faq-answer {
  @apply px-3 lg:px-5 pb-3 lg:pb-5 text-[#ffffffad];
}

@keyframes verticalLoop {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

.timeline {
  position: relative;
  width: 100%;
  max-width: 1140px;
  margin: 0 auto;
  padding: 15px 0;
}

.timeline::after {
  content: "";
  position: absolute;
  width: 2.5px;
  background: linear-gradient(to bottom, #1c1d1c00, #4a4a4a, #1d1e1d00);
  top: 0;
  bottom: 0;
  left: 50%;
  margin-left: -1px;
  @apply hidden lg:block;
}

.timeline__container {
  /* position: relative;
  background: inherit;
  width: 50%; */
  @apply relative w-full lg:w-1/2 mb-4 lg:mb-0;
}

.timeline__container.left {
  left: 0;
  border-radius: 15px;
}

.timeline__container.right {
  left: 57.5%;
  border-radius: 15px;
}

.timeline__container::after {
  content: url("/assets/frontend_assets/dot.svg");
  position: absolute;

  top: calc(50% - 13%);
  right: -3.5%;
  z-index: 1;
  @apply hidden lg:block;
}

.timeline__container.right::after {
  left: -18.5%;
}

.timeline__container:before {
  content: "";
  position: absolute;
  width: 78px;
  height: 2.5px;
  top: calc(50% - -1px);
  right: 1.5%;
  background: #fff3;
  z-index: 1;
  @apply hidden lg:block;
}

.timeline__container.right::before {
  left: -13%;
}

.timeline__container .content {
  @apply w-full lg:w-[85%] h-[135px] rounded-[15px] py-4 px-3;
}

.timeline__container.right .content {
  @apply p-[30px] ml-0 lg:ml-1;
}

@media (max-width: 767.98px) {
  .timeline::after {
    left: 90px;
  }

  .timeline__container {
    width: 100%;
    padding-left: 120px;
    padding-right: 30px;
    @apply w-full lg:pl-[120px] lg:pr-[30px] px-4;
  }

  .timeline__container.right {
    left: 0%;
  }

  .timeline__container.left::after,
  .timeline__container.right::after {
    left: 82px;
  }

  .timeline__container.left::before,
  .timeline__container.right::before {
    left: 100px;
    border-color: transparent #74ac37 transparent transparent;
  }

  .timeline__container.left .content,
  .timeline__container.right .content {
    padding: 20px;
    border-radius: 12px;
  }
}

.card-2 {
  display: flex;
  flex-flow: column;
  align-items: flex-start;
  padding: 2rem 1.5rem;
  border-radius: 1.5rem;
  min-height: 422px;
}

/* .custom-swiper {
	overflow: visible;
	padding: 20px 0;
}

.swiper-wrapper {
	align-items: center;
	height: 460px;
}

.swiper-slide {
	opacity: 1 !important;
	transition: transform 0.4s ease !important;
	position: relative;
	z-index: 1;
} */
.custom-swiper {
  overflow: visible;
  padding: 20px 0;
  margin-left: 100px;
}

.swiper-wrapper {
  align-items: center;
  height: 460px;
}

.swiper-slide {
  opacity: 1 !important;
  transition: none !important; /* Let GSAP handle all animations */
}
.testimonial_slider .swiper {
  @apply py-10;
}
.pulse__black {
  border-radius: 50%;
  color: #fff;
  height: 68px;
  margin: auto;
  position: relative;
  width: 68px;
}
.pulse__black:after,
.pulse__black:before {
  background-color: #fff;
  border-radius: 50%;
  content: "";
  height: 100%;
  opacity: 0.7;
  position: absolute;
  width: 100%;
  z-index: -1;
  top: 0;
}
.pulse__black:before {
  animation: pulse_black 5s ease-out infinite;
}
.pulse__black:after {
  animation: pulse_black 5s 1s ease-out infinite;
}
@keyframes pulse_black {
  100% {
    transform: scale(2.5);
    opacity: 0;
  }
}
.dangerous__html h1 {
  font-weight: 700;
  font-size: 48px;
  color: #c0c5d0;
  margin-top: 16px;
  font-family: "Inter", sans-serif;
}
.dangerous__html h2 {
  font-weight: 700;
  font-size: 30px;
  color: #c0c5d0;
  margin-top: 16px;

  font-family: "Inter", sans-serif;
}
.dangerous__html h3 {
  font-weight: 700;
  font-size: 24px;
  color: #c0c5d0;
  margin-top: 16px;

  font-family: "Inter", sans-serif;
}
.dangerous__html h4 {
  font-weight: 700;
  font-size: 20px;
  color: #c0c5d0;
  margin-top: 16px;

  font-family: "Inter", sans-serif;
}
.dangerous__html h5 {
  font-weight: 700;
  font-size: 18px;
  color: #c0c5d0;
  margin-top: 16px;

  font-family: "Inter", sans-serif;
}
.dangerous__html h6 {
  font-weight: 700;
  font-size: 16px;
  color: #c0c5d0;
  margin-top: 16px;

  font-family: "Inter", sans-serif;
}
.dangerous__html p {
  font-weight: 400;
  font-size: 16px;
  color: #c0c5d0;
  margin-top: 16px;

  font-family: "Inter", sans-serif;
}
.dangerous__html p span {
  font-weight: 600;
  font-size: 16px;
  color: #c0c5d0;
  font-family: "Inter", sans-serif;
}
.dangerous__html img {
  width: 100%;
  height: auto;
  margin-top: 20px;
  margin-bottom: 20px;
}

.logoMarqueeSection {
  padding-top: 50vh;
  padding-bottom: 150vh;
}

#logoMarqueeSection {
  /* max-width: 1920px !important; */
  /* margin: 0 auto; */
}

.default-content-container {
  /* margin-left: auto;
  margin-right: auto; */
  margin-top: 0;
  margin-bottom: 0;
  /* padding-left: 5rem; */
  /* padding-right: 5rem; */
  padding-top: 3rem;
  padding-bottom: 3rem;
  width: 100%;
  /* min-height: 100vh; */
}

div.marquee > img {
  height: 50px;
}

.logoMarqueeSection > div > div {
  padding-top: 0;
  padding-bottom: 0;
  min-height: 0;
}

.marquee-wrapper {
  display: inline-block;
  white-space: nowrap;
}

.marquee {
  display: inline-block;
  white-space: nowrap;
  position: relative;
  transform: translate3d(0%, 0, 0);
  animation-name: marquee;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

.marquee img {
  display: inline-block;
  white-space: nowrap;
  padding-right: 5.4rem;
}

.marquee-wrapper:hover .marquee {
  animation-play-state: paused !important;
}

@keyframes marquee {
  0% {
    transform: translate3d(0%, 0, 0);
  }

  100% {
    transform: translate3d(-100%, 0, 0);
  }
}

@-webkit-keyframes moveInCircle {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes moveInCircle {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@-webkit-keyframes moveVertical {
  0% {
    transform: translateY(-50%);
  }
  50% {
    transform: translateY(50%);
  }
  100% {
    transform: translateY(-50%);
  }
}
@keyframes moveVertical {
  0% {
    transform: translateY(-50%);
  }
  50% {
    transform: translateY(50%);
  }
  100% {
    transform: translateY(-50%);
  }
}
@-webkit-keyframes moveHorizontal {
  0% {
    transform: translateX(-50%) translateY(-10%);
  }
  50% {
    transform: translateX(50%) translateY(10%);
  }
  100% {
    transform: translateX(-50%) translateY(-10%);
  }
}
@keyframes moveHorizontal {
  0% {
    transform: translateX(-50%) translateY(-10%);
  }
  50% {
    transform: translateX(50%) translateY(10%);
  }
  100% {
    transform: translateX(-50%) translateY(-10%);
  }
}
.gradient-bg {
  width: 100vw;
  height: 100vh;
  position: absolute;
  overflow: hidden;
  /* background: linear-gradient(40deg, var(--color-bg1), var(--color-bg2)); */
  top: 0;
  left: 0;
  filter: blur(100px);
}
.gradient-bg svg {
  position: fixed;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
}
.gradient-bg .gradients-container {
  filter: url(#goo) blur(40px);
  width: 100%;
  height: 100%;
}
.gradient-bg .g1 {
  position: absolute;
  /* background: radial-gradient(
      circle at center,
      rgba(var(--color1), 0.8) 0,
      rgba(var(--color1), 0) 50%
    )
    no-repeat; */
  background: radial-gradient(
      circle at center,
      rgb(11 49 17 / 80%) 0,
      rgba(var(--color1), 0) 50%
    )
    no-repeat;
  filter: blur(100px);

  mix-blend-mode: var(--blending);
  width: var(--circle-size);
  height: var(--circle-size);
  top: calc(50% - var(--circle-size) / 2);
  left: calc(50% - var(--circle-size) / 2);
  transform-origin: center center;
  -webkit-animation: moveVertical 30s ease infinite;
  animation: moveVertical 30s ease infinite;
  opacity: 1;
}
.gradient-bg .g2 {
  position: absolute;
  /* background: radial-gradient(
      circle at center,
      rgba(var(--color2), 0.8) 0,
      rgba(var(--color2), 0) 50%
    )
    no-repeat; */
  background: radial-gradient(
      circle at center,
      rgb(31 74 30 / 80%) 0,
      rgba(var(--color2), 0) 50%
    )
    no-repeat;
  filter: blur(100px);
  mix-blend-mode: var(--blending);
  width: var(--circle-size);
  height: var(--circle-size);
  top: calc(50% - var(--circle-size) / 2);
  left: calc(50% - var(--circle-size) / 2);
  transform-origin: calc(50% - 400px);
  animation: moveInCircle 20s reverse infinite;
  opacity: 1;
}
.gradient-bg .g3 {
  position: absolute;
  background: radial-gradient(
      circle at center,
      rgb(25 70 49 / 80%) 0,
      rgba(var(--color3), 0) 50%
    )
    no-repeat;
  filter: blur(100px);
  mix-blend-mode: var(--blending);
  width: var(--circle-size);
  height: var(--circle-size);
  top: calc(50% - var(--circle-size) / 2 + 200px);
  left: calc(50% - var(--circle-size) / 2 - 500px);
  transform-origin: calc(50% + 400px);
  -webkit-animation: moveInCircle 40s linear infinite;
  animation: moveInCircle 40s linear infinite;
  opacity: 1;
}
.gradient-bg .g4 {
  position: absolute;
  background: radial-gradient(
      circle at center,
      rgb(24 55 26 / 80%) 0,
      rgba(var(--color4), 0) 50%
    )
    no-repeat;
  filter: blur(100px);
  mix-blend-mode: var(--blending);
  width: var(--circle-size);
  height: var(--circle-size);
  top: calc(50% - var(--circle-size) / 2);
  left: calc(50% - var(--circle-size) / 2);
  transform-origin: calc(50% - 200px);
  -webkit-animation: moveHorizontal 40s ease infinite;
  animation: moveHorizontal 40s ease infinite;
  opacity: 0.7;
}
.gradient-bg .g5 {
  position: absolute;
  /* background: radial-gradient(
      circle at center,
      rgba(var(--color5), 0.8) 0,
      rgba(var(--color5), 0) 50%
    )
    no-repeat; */
  background: radial-gradient(
      circle at center,
      rgb(24 40 21 / 80%) 0,
      rgba(var(--color5), 0) 50%
    )
    no-repeat;
  filter: blur(100px);
  mix-blend-mode: var(--blending);
  width: calc(var(--circle-size) * 2);
  height: calc(var(--circle-size) * 2);
  top: calc(50% - var(--circle-size));
  left: calc(50% - var(--circle-size));
  transform-origin: calc(50% - 800px) calc(50% + 200px);
  -webkit-animation: moveInCircle 20s ease infinite;
  animation: moveInCircle 20s ease infinite;
  opacity: 1;
}
.gradient-bg .interactive {
  position: absolute;
  /* background: radial-gradient(
      circle at center,
      rgba(var(--color-interactive), 0.8) 0,
      rgba(var(--color-interactive), 0) 50%
    )
    no-repeat; */
  background: radial-gradient(
      circle at center,
      rgb(25 53 27 / 80%) 0,
      rgba(var(--color-interactive), 0) 50%
    )
    no-repeat;
  filter: blur(100px);
  mix-blend-mode: var(--blending);
  width: 100%;
  height: 100%;
  top: -50%;
  left: -50%;
  opacity: 0.7;
}
/* animation */

.section-title {
  position: relative;
  margin-bottom: 0;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.3em;
  z-index: 1;
}

.text-hightlight {
  color: #ea2227; /* Or your highlight color */
}
.left-menu-item .div__content {
  text-align: center;
  cursor: pointer;
  user-select: none;
  position: relative;
  z-index: 10;
  background: #1c1c1c;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #4a4a4a;
}
/* .left-tab-menu .left-menu-item.active {
  color: #9ad35b;
} */
.left-tab-menu .left-menu-item {
  border-radius: 10px;
  overflow: hidden;
}
.left-menu-item {
  position: relative;
  overflow: hidden;
  padding: 1px;
}
.left-menu-item.active::before {
  display: block;
  animation: 1.5s linear 1s 1 normal forwards running bg1;
}
.left-menu-item.active::after {
  display: block;
  animation: 3s linear 1s 1 normal forwards running bg2;
}
.left-menu-item::after,
.left-menu-item::before {
  z-index: 3;
  content: "";
  left: 1px;
  top: 0px;
  position: absolute;
  display: none;
  width: 100%;
  height: calc(100% - 1px);
  border-radius: 10px;
  background: #1c1c1c;
}
.left-menu-item::after {
  left: unset !important;
  top: unset !important;
  bottom: 0px !important;
  right: 0px !important;
}
@keyframes bg1 {
  0% {
    width: 0px;
    height: 10px;
    background: #9ad35b;
  }
  50% {
    width: 100%;
    height: 10px;
    background: #9ad35b;
  }
  100% {
    width: 100%;
    height: calc(100% - 1px);
    background: #9ad35b;
  }
}

@keyframes bg2 {
  0% {
    width: 0px;
    height: 10px;
    background: #9ad35b;
  }
  50% {
    width: 0px;
    height: 10px;
    background: #9ad35b;
  }
  75% {
    width: 100%;
    height: 10px;
    background: #9ad35b;
  }
  100% {
    width: 100%;
    height: calc(100% - 1px);
    background: #9ad35b;
  }
}
