import { Geist, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/context/AuthContext";
import { NavigationProvider } from "@/context/NavigationContext";
import ToastProvider from "@/components/common/ToastProvider";
import ConditionalNavigationLoader from "@/components/common/ConditionalNavigationLoader";
import ApiLoaderInitializer from "@/components/common/ApiLoaderInitializer";
// Import toast utility to make it available globally
import "@/utils/toast";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "Qacent",
  description: "Qacent",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} bg-[#1C1C1C] antialiased`}
        suppressHydrationWarning
      >
        <AuthProvider>
          <NavigationProvider>
            <ToastProvider />
            <ConditionalNavigationLoader />
            <ApiLoaderInitializer />
            {children}
          </NavigationProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
