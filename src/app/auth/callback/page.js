"use client";

import { Suspense, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import toast from "react-hot-toast";

function Loader() {
  return (
    <div className="flex justify-center items-center h-96">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#648A3A]" />
    </div>
  );
}

function AuthCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const token = searchParams.get("token");
    const user = searchParams.get("user");
    const error = searchParams.get("error");

    if (error) {
      toast.error(`Authentication failed: ${error}`);
      router.push("/login");
      return;
    }

    if (token && user) {
      try {
        const userData = JSON.parse(decodeURIComponent(user));
        localStorage.setItem("access_token", token);
        localStorage.setItem("token_type", "Bearer");
        localStorage.setItem("user", JSON.stringify(userData));

        toast.success("Login successful!");
        location.replace("/dashboard");
      } catch (err) {
        console.error("Error parsing user data:", err);
        toast.error("Authentication failed");
        router.push("/login");
      }
    } else {
      toast.error("Authentication failed");
      router.push("/login");
    }
  }, [searchParams, router]);

  // ⚠ No need to return Loader here!
  return null;
}

export default function AuthCallback() {
  return (
    <Suspense fallback={<Loader />}>
      <AuthCallbackContent />
    </Suspense>
  );
}
