export function filesize(size) {
  const i = Math.floor(Math.log(size) / Math.log(1024));
  return (
    (size / Math.pow(1024, i)).toFixed(2) * 1 +
    " " +
    ["B", "kB", "MB", "GB", "TB"][i]
  );
}

// Transforms key/value pairs to FormData() object
export function toFormData(values = {}, method = "POST", isFormData = true) {
  let formData;
  if (isFormData) {
    formData = new FormData();
    for (const field of Object.keys(values)) {
      formData.append(field, values[field]);
    }

    // NOTE: When working with <PERSON><PERSON> PUT/PATCH requests and FormData
    // you SHOULD send POST request and fake the PUT request like this.
    // More info: http://stackoverflow.com/q/50691938
    if (method.toUpperCase() === "PUT") {
      formData.append("_method", "PUT");
    }
  } else {
    formData = {};
    for (const field of Object.keys(values)) {
      formData[field] = values[field];
    }

    if (method.toUpperCase() === "PUT") {
      formData["_method"] = "PUT";
    }
  }
  return formData;
}
