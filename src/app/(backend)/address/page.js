"use client";
import AddressModal from "@/components/backend/AddressModal";
import React, { useState, useEffect, Suspense } from "react";
import apiService from "@/services/api";
import toast from "react-hot-toast";
import { PencilIcon, TrashIcon } from "@heroicons/react/24/outline";
import Breadcrumb from "@/components/backend/Breadcrumb";
import { useSearchParams } from "next/navigation";

// Create a client component that uses useSearchParams
const SearchParamsHandler = () => {
  const searchParams = useSearchParams();
  // You can use searchParams here if needed
  return null;
};

const Address = () => {
  const [showAddressModal, setShowAddressModal] = useState(false);
  const [editAddressId, setEditAddressId] = useState(null);
  const [networkAddresses, setNetworkAddresses] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteAddressId, setDeleteAddressId] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Function to handle edit button click
  const handleEditAddress = (addressId) => {
    setEditAddressId(addressId);
    setShowAddressModal(true);
  };

  // Function to handle delete button click
  const handleDeleteAddress = (addressId) => {
    setDeleteAddressId(addressId);
    setShowDeleteModal(true);
  };

  // Function to confirm and execute delete
  const confirmDeleteAddress = async () => {
    if (!deleteAddressId) return;

    setIsDeleting(true);
    try {
      const response = await apiService.deleteNetworkAddress(deleteAddressId);

      if (response.status) {
        // Remove the deleted address from the local state
        setNetworkAddresses((prev) =>
          prev.filter((address) => address.id !== deleteAddressId)
        );
        toast.success("Address deleted successfully");
        setShowDeleteModal(false);
        setDeleteAddressId(null);
      } else {
        toast.error(response.message || "Failed to delete address");
      }
    } catch (error) {
      console.error("Error deleting address:", error);
      toast.error("An error occurred while deleting the address");
    } finally {
      setIsDeleting(false);
    }
  };

  // Function to cancel delete
  const cancelDelete = () => {
    setShowDeleteModal(false);
    setDeleteAddressId(null);
  };

  // Fetch network addresses from API
  useEffect(() => {
    const fetchNetworkAddresses = async () => {
      setIsLoading(true);
      try {
        const response = await apiService.getNetworkAddresses();
        if (response.data) {
          setNetworkAddresses(response.data);
        } else {
          setError("Failed to fetch Crypto address");
        }
      } catch (err) {
        console.error("Error fetching Crypto address:", err);
        setError("An error occurred while fetching Crypto address");
        toast.error("Failed to load Crypto address");
      } finally {
        setIsLoading(false);
      }
    };

    fetchNetworkAddresses();
  }, []);

  return (
    <>
      {/* Wrap useSearchParams in Suspense */}
      <Suspense fallback={null}>
        <SearchParamsHandler />
      </Suspense>
      <Breadcrumb />
      <div className="bg-gradient-to-b from-[#9EDA581A] via-[#68DB9F1A] to-[#2020201A] rounded-[32px] p-8">
        <div className="mb-5">
          <h2 className="text-xl font-semibold inter text-white mb-1">
            Crypto address{" "}
          </h2>
          <p className="text-xs font-semibold inter text-[#777576]">
            Details information of your Crypto address{" "}
          </p>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#648A3A]"></div>
          </div>
        ) : error ? (
          <div className="text-red-500 text-center py-4">{error}</div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Map through network addresses from API */}
            {networkAddresses.map((address) => (
              <div
                key={address.id}
                className="bg-no-repeat bg-center bg-auto rounded-[20px] h-40 p-4 overflow-hidden relative border border-white/50 group transition-all duration-300 ease-in-out"
                style={{
                  backgroundImage:
                    "url('/assets/backend_assets/images/address-card-bg.png')",
                }}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="kronaOne font-normal text-xl text-white">
                    {address.network?.name || "Network"}
                  </h3>

                  <div className="w-9 h-9 bg-[#FFFFFF]/50 rounded-full flex items-center justify-center">
                    <svg
                      width={20}
                      height={20}
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g clipPath="url(#clip0_711_2498)">
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M20 10C20 15.5228 15.515 20 9.98258 20C4.73366 20 0.427652 15.9701 0 10.8405H13.2408V9.15938H0C0.427652 4.02992 4.73366 0 9.98258 0C15.515 0 20 4.47714 20 10Z"
                          fill="#0052FF"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_711_2498">
                          <rect width={20} height={20} fill="white" />
                        </clipPath>
                      </defs>
                    </svg>
                  </div>
                  {/* Status badge */}
                  <div className="hidden space-x-2 group-hover:flex items-center">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditAddress(address.id);
                      }}
                      className="bg-white/20 p-1 rounded-full hover:bg-white/30 transition-colors"
                      title="Edit Address"
                    >
                      <PencilIcon className="h-3 w-3 " />
                    </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteAddress(address.id);
                    }}
                    className="bg-white/20 p-1 rounded-full hover:bg-white/30 transition-colors"
                    title="Delete Address"
                  >
                    <TrashIcon color="red" className="h-3 w-3 " />
                    </button>
                                      </div>

                </div>

                {/* address name */}

                <div className="absolute top-14 left-4">
                  <h4 className="gradient_text inter font-bold text-lg">
                    {address.name || "N/A"}
                  </h4>
                </div>

                <div className="bg-white/20 absolute bottom-0 left-0 w-full px-4 py-3 flex justify-between items-center rounded-b-[16px]">
                  <div className="">
                    <p className="inter text-[7px] font-normal text-white">
                      Currency
                    </p>
                    <p className="inter text-[13px] font-normal text-white">
                      {address.currency?.name || "N/A"}
                    </p>
                  </div>
                  <div className="">
                    <p className="inter text-[7px] font-normal text-white">
                      Crypto address
                    </p>
                    <p className="inter text-[13px] font-normal text-white truncate max-w-[120px]">
                      {address.network_address
                        ? `${address.network_address.slice(
                            0,
                            8
                          )}...${address.network_address.slice(-8)}`
                        : ""}
                    </p>
                  </div>
                </div>
              </div>
            ))}

            {/* Add New Address button */}
            <div
              className="bg-[#9EDA581A] rounded-[20px] h-40 p-4 overflow-hidden relative text-white flex items-center justify-center flex-col border border-[#648a3a] cursor-pointer"
              onClick={() => {
                setEditAddressId(null);
                setShowAddressModal(true);
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="size-8"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 4.5v15m7.5-7.5h-15"
                />
              </svg>
              <p className="inter font-medium text-base text-[#FFFBFD] mt-3">
                Add New Address
              </p>
            </div>
          </div>
        )}
      </div>
      {showAddressModal && (
        <AddressModal
          setShowAddressModal={setShowAddressModal}
          editAddressId={editAddressId}
        />
      )}
    </>
  );
};

export default Address;
