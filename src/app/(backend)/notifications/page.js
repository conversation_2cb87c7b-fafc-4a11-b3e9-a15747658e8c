"use client";

import { useState, useEffect } from "react";
import { notificationAPI } from "@/components/notifications";
import toast from "react-hot-toast";

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filter, setFilter] = useState('');

  // Fetch notifications
  const fetchNotifications = async () => {
    try {
      setIsLoading(true);

      // Get notifications
      const notificationsResponse = await notificationAPI.getUserNotifications({
        page: currentPage,
        perPage: 20,
        is_read: filter,
        sortBy: 'created_at',
        sortOrder: 'desc'
      });

      if (notificationsResponse.status && notificationsResponse.data) {
        setNotifications(notificationsResponse.data.data || []);
        setTotalPages(notificationsResponse.data.last_page || 1);
      }

      // Get unread count
      const countResponse = await notificationAPI.getUnreadCount();
      if (countResponse.status && countResponse.data) {
        setUnreadCount(countResponse.data.unread_count || 0);
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
      toast.error("Failed to load notifications");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle mark as read
  const handleMarkAsRead = async (notificationId, isRead = true) => {
    try {
      await notificationAPI.markAsRead(notificationId, isRead);

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, is_read: isRead }
            : notification
        )
      );

      // Update unread count
      setUnreadCount(prev => isRead ? Math.max(0, prev - 1) : prev + 1);
      toast.success(`Notification marked as ${isRead ? 'read' : 'unread'}`);
    } catch (error) {
      console.error("Error updating notification:", error);
      toast.error("Failed to update notification");
    }
  };

  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    try {
      const unreadNotifications = notifications.filter(n => !n.is_read);
      if (unreadNotifications.length === 0) {
        toast.info("No unread notifications");
        return;
      }

      const notificationIds = unreadNotifications.map(n => n.id);
      await notificationAPI.bulkMarkAsRead(notificationIds, true);

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, is_read: true }))
      );

      setUnreadCount(0);
      toast.success("All notifications marked as read");
    } catch (error) {
      console.error("Error marking all as read:", error);
      toast.error("Failed to mark all as read");
    }
  };

  // Format relative time
  const formatRelativeTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

    return date.toLocaleDateString();
  };

  // Fetch notifications on component mount and when filters change
  useEffect(() => {
    fetchNotifications();
  }, [currentPage, filter]);

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">
          Notifications {unreadCount > 0 && `(${unreadCount} unread)`}
        </h1>
        <p className="text-gray-400">Manage your notifications and stay updated</p>
      </div>

      <div className="bg-[#282828] rounded-xl p-6">
        {/* Header with filters and actions */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <select
              value={filter}
              onChange={(e) => {
                setFilter(e.target.value);
                setCurrentPage(1);
              }}
              className="bg-[#1a1a1a] text-white border border-gray-600 rounded-lg px-3 py-2"
            >
              <option value="">All notifications</option>
              <option value="false">Unread only</option>
              <option value="true">Read only</option>
            </select>
          </div>

          {unreadCount > 0 && (
            <button
              onClick={handleMarkAllAsRead}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Mark all as read
            </button>
          )}
        </div>

        {/* Content */}
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p className="text-gray-400 mt-4">Loading notifications...</p>
          </div>
        ) : notifications.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12">
            <svg
              className="w-16 h-16 text-gray-500 mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
              />
            </svg>
            <h3 className="text-lg font-medium text-white mb-2">No notifications found</h3>
            <p className="text-gray-400">
              {filter === 'false' ? 'No unread notifications' :
               filter === 'true' ? 'No read notifications' :
               'You have no notifications yet'}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 rounded-lg border transition-colors ${
                  !notification.is_read
                    ? 'bg-blue-500/10 border-blue-500/30'
                    : 'bg-gray-700/30 border-gray-600/30'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className={`text-white ${!notification.is_read ? 'font-medium' : ''}`}>
                      {notification.text}
                    </p>
                    <p className="text-gray-400 text-sm mt-2">
                      {formatRelativeTime(notification.created_at)}
                    </p>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    {!notification.is_read ? (
                      <button
                        onClick={() => handleMarkAsRead(notification.id, true)}
                        className="text-blue-400 hover:text-blue-300 text-sm px-3 py-1 rounded border border-blue-400/30 hover:border-blue-300/30 transition-colors"
                      >
                        Mark as read
                      </button>
                    ) : (
                      <button
                        onClick={() => handleMarkAsRead(notification.id, false)}
                        className="text-gray-400 hover:text-gray-300 text-sm px-3 py-1 rounded border border-gray-600/30 hover:border-gray-500/30 transition-colors"
                      >
                        Mark as unread
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center items-center gap-4 mt-8">
            <button
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              className="px-4 py-2 bg-gray-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 transition-colors"
            >
              Previous
            </button>

            <span className="text-gray-400">
              Page {currentPage} of {totalPages}
            </span>

            <button
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
              className="px-4 py-2 bg-gray-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 transition-colors"
            >
              Next
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
